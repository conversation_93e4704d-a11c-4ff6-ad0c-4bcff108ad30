const DepartmentCard = ({ department }) => {
  if (!department) return null;

  if (department.code === -1) {
    return (
      <div className="card mb-4 border-danger">
        <div className="card-header bg-danger text-white">
          <h5 className="card-title mb-0">Department</h5>
        </div>
        <div className="card-body">
          <p className="text-danger">Error loading department data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="card mb-4">
      <div className="card-header bg-primary text-white">
        <h5 className="card-title mb-0">Department</h5>
      </div>
      <div className="card-body">
        <p><strong>Name:</strong> {department.name}</p>
        <p><strong>Code:</strong> {department.code}</p>
      </div>
    </div>
  );
};

export default DepartmentCard;
