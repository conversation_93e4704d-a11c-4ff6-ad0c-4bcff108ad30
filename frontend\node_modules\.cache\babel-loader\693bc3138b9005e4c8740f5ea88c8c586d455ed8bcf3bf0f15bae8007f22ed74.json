{"ast": null, "code": "import AuthService from './AuthService';\n\n// Base URL for BFF API calls\nconst BFF_BASE_URL = 'http://localhost:4000/api/employees'; // Node.js BFF endpoint\n\n// Create authenticated axios instance using AuthService\nconst apiClient = AuthService.createAuthenticatedClient(BFF_BASE_URL);\n\n// Service methods\nconst EmployeeService = {\n  /**\r\n   * Fetches all employee, department, and manager data via BFF.\r\n   * @returns {Promise<Object[]>}\r\n   */\n  async getEmployeeData() {\n    try {\n      console.log('🔄 Fetching all employee data via BFF...');\n      const response = await apiClient.get('/'); // BFF endpoint for all employees\n      console.log('✅ BFF Response received:', response.data);\n      return response.data.data || response.data; // Handle BFF response structure\n    } catch (error) {\n      console.error('❌ Error fetching employee data via BFF:', error);\n      if (error.response) {\n        var _error$response$data, _error$response$data2, _error$response$data3;\n        const errorMessage = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : (_error$response$data3 = _error$response$data2.error) === null || _error$response$data3 === void 0 ? void 0 : _error$response$data3.message) || `BFF error: ${error.response.status}`;\n        throw new Error(errorMessage);\n      } else if (error.request) {\n        throw new Error('No response from BFF server. Please check your connection.');\n      } else {\n        throw new Error('Error: ' + error.message);\n      }\n    }\n  },\n  /**\r\n   * Fetches data for a specific employee ID via BFF.\r\n   * @param {number} employeeId\r\n   * @returns {Promise<Object[]>}\r\n   */\n  async getEmployeeDataById(employeeId) {\n    try {\n      console.log(`🔄 Fetching employee data for ID: ${employeeId} via BFF...`);\n      const response = await apiClient.get(`/${employeeId}`); // BFF endpoint for specific employee\n      console.log('✅ BFF Response received:', response.data);\n      const data = response.data.data || response.data;\n      return Array.isArray(data) ? data : [data];\n    } catch (error) {\n      console.error('❌ Error in getEmployeeDataById via BFF:', error);\n      if (error.response) {\n        var _error$response$data4, _error$response$data5, _error$response$data6;\n        const errorMessage = ((_error$response$data4 = error.response.data) === null || _error$response$data4 === void 0 ? void 0 : _error$response$data4.message) || ((_error$response$data5 = error.response.data) === null || _error$response$data5 === void 0 ? void 0 : (_error$response$data6 = _error$response$data5.error) === null || _error$response$data6 === void 0 ? void 0 : _error$response$data6.message) || `BFF error: ${error.response.status} - ${error.response.statusText}`;\n        throw new Error(errorMessage);\n      } else if (error.request) {\n        throw new Error('No response from BFF server. Please check if the BFF is running and accessible.');\n      } else {\n        throw new Error('Error fetching employee by ID: ' + error.message);\n      }\n    }\n  },\n  /**\r\n   * Inserts new employee, department, and manager records via BFF.\r\n   * @param {Object} payload\r\n   * @returns {Promise<string>}\r\n   */\n  async insertEmployeeData(payload) {\n    try {\n      console.log('🔄 Inserting employee data via BFF...', payload);\n      const response = await apiClient.post('/', payload); // BFF endpoint for insert\n      console.log('✅ BFF Insert response:', response.data);\n      return response.data.data || response.data.message || response.data;\n    } catch (error) {\n      console.error('❌ Error inserting employee data via BFF:', error);\n      if (error.response) {\n        var _error$response$data7, _error$response$data8, _error$response$data9;\n        const errorMessage = ((_error$response$data7 = error.response.data) === null || _error$response$data7 === void 0 ? void 0 : _error$response$data7.message) || ((_error$response$data8 = error.response.data) === null || _error$response$data8 === void 0 ? void 0 : (_error$response$data9 = _error$response$data8.error) === null || _error$response$data9 === void 0 ? void 0 : _error$response$data9.message) || `BFF insert failed: ${error.response.status}`;\n        throw new Error(errorMessage);\n      } else if (error.request) {\n        throw new Error('Insert request failed. No BFF server response.');\n      } else {\n        throw new Error('Insert error: ' + error.message);\n      }\n    }\n  }\n};\nexport default EmployeeService;", "map": {"version": 3, "names": ["AuthService", "BFF_BASE_URL", "apiClient", "createAuthenticatedClient", "EmployeeService", "getEmployeeData", "console", "log", "response", "get", "data", "error", "_error$response$data", "_error$response$data2", "_error$response$data3", "errorMessage", "message", "status", "Error", "request", "getEmployeeDataById", "employeeId", "Array", "isArray", "_error$response$data4", "_error$response$data5", "_error$response$data6", "statusText", "insertEmployeeData", "payload", "post", "_error$response$data7", "_error$response$data8", "_error$response$data9"], "sources": ["D:/FRONT-END-BACK-END-COMMUNICATION/frontend/src/services/EmployeeService.js"], "sourcesContent": ["import AuthService from './AuthService';\r\n\r\n// Base URL for BFF API calls\r\nconst BFF_BASE_URL = 'http://localhost:4000/api/employees'; // Node.js BFF endpoint\r\n\r\n// Create authenticated axios instance using AuthService\r\nconst apiClient = AuthService.createAuthenticatedClient(BFF_BASE_URL);\r\n\r\n// Service methods\r\nconst EmployeeService = {\r\n  /**\r\n   * Fetches all employee, department, and manager data via BFF.\r\n   * @returns {Promise<Object[]>}\r\n   */\r\n  async getEmployeeData() {\r\n    try {\r\n      console.log('🔄 Fetching all employee data via BFF...');\r\n      const response = await apiClient.get('/'); // BFF endpoint for all employees\r\n      console.log('✅ BFF Response received:', response.data);\r\n      return response.data.data || response.data; // Handle BFF response structure\r\n    } catch (error) {\r\n      console.error('❌ Error fetching employee data via BFF:', error);\r\n      if (error.response) {\r\n        const errorMessage = error.response.data?.message ||\r\n          error.response.data?.error?.message ||\r\n          `BFF error: ${error.response.status}`;\r\n        throw new Error(errorMessage);\r\n      } else if (error.request) {\r\n        throw new Error('No response from BFF server. Please check your connection.');\r\n      } else {\r\n        throw new Error('Error: ' + error.message);\r\n      }\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Fetches data for a specific employee ID via BFF.\r\n   * @param {number} employeeId\r\n   * @returns {Promise<Object[]>}\r\n   */\r\n  async getEmployeeDataById(employeeId) {\r\n    try {\r\n      console.log(`🔄 Fetching employee data for ID: ${employeeId} via BFF...`);\r\n      const response = await apiClient.get(`/${employeeId}`); // BFF endpoint for specific employee\r\n      console.log('✅ BFF Response received:', response.data);\r\n\r\n      const data = response.data.data || response.data;\r\n      return Array.isArray(data) ? data : [data];\r\n    } catch (error) {\r\n      console.error('❌ Error in getEmployeeDataById via BFF:', error);\r\n      if (error.response) {\r\n        const errorMessage = error.response.data?.message ||\r\n          error.response.data?.error?.message ||\r\n          `BFF error: ${error.response.status} - ${error.response.statusText}`;\r\n        throw new Error(errorMessage);\r\n      } else if (error.request) {\r\n        throw new Error('No response from BFF server. Please check if the BFF is running and accessible.');\r\n      } else {\r\n        throw new Error('Error fetching employee by ID: ' + error.message);\r\n      }\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Inserts new employee, department, and manager records via BFF.\r\n   * @param {Object} payload\r\n   * @returns {Promise<string>}\r\n   */\r\n  async insertEmployeeData(payload) {\r\n    try {\r\n      console.log('🔄 Inserting employee data via BFF...', payload);\r\n      const response = await apiClient.post('/', payload); // BFF endpoint for insert\r\n      console.log('✅ BFF Insert response:', response.data);\r\n      return response.data.data || response.data.message || response.data;\r\n    } catch (error) {\r\n      console.error('❌ Error inserting employee data via BFF:', error);\r\n      if (error.response) {\r\n        const errorMessage = error.response.data?.message ||\r\n          error.response.data?.error?.message ||\r\n          `BFF insert failed: ${error.response.status}`;\r\n        throw new Error(errorMessage);\r\n      } else if (error.request) {\r\n        throw new Error('Insert request failed. No BFF server response.');\r\n      } else {\r\n        throw new Error('Insert error: ' + error.message);\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nexport default EmployeeService;\r\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;;AAEvC;AACA,MAAMC,YAAY,GAAG,qCAAqC,CAAC,CAAC;;AAE5D;AACA,MAAMC,SAAS,GAAGF,WAAW,CAACG,yBAAyB,CAACF,YAAY,CAAC;;AAErE;AACA,MAAMG,eAAe,GAAG;EACtB;AACF;AACA;AACA;EACE,MAAMC,eAAeA,CAAA,EAAG;IACtB,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,MAAMC,QAAQ,GAAG,MAAMN,SAAS,CAACO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3CH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,QAAQ,CAACE,IAAI,CAAC;MACtD,OAAOF,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAIA,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAAI,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QAClB,MAAMC,YAAY,GAAG,EAAAH,oBAAA,GAAAD,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAE,oBAAA,uBAAnBA,oBAAA,CAAqBI,OAAO,OAAAH,qBAAA,GAC/CF,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAG,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBF,KAAK,cAAAG,qBAAA,uBAA1BA,qBAAA,CAA4BE,OAAO,KACnC,cAAcL,KAAK,CAACH,QAAQ,CAACS,MAAM,EAAE;QACvC,MAAM,IAAIC,KAAK,CAACH,YAAY,CAAC;MAC/B,CAAC,MAAM,IAAIJ,KAAK,CAACQ,OAAO,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,4DAA4D,CAAC;MAC/E,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAAC,SAAS,GAAGP,KAAK,CAACK,OAAO,CAAC;MAC5C;IACF;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAMI,mBAAmBA,CAACC,UAAU,EAAE;IACpC,IAAI;MACFf,OAAO,CAACC,GAAG,CAAC,qCAAqCc,UAAU,aAAa,CAAC;MACzE,MAAMb,QAAQ,GAAG,MAAMN,SAAS,CAACO,GAAG,CAAC,IAAIY,UAAU,EAAE,CAAC,CAAC,CAAC;MACxDf,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAEtD,MAAMA,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI;MAChD,OAAOY,KAAK,CAACC,OAAO,CAACb,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;IAC5C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAIA,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAAgB,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QAClB,MAAMX,YAAY,GAAG,EAAAS,qBAAA,GAAAb,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAc,qBAAA,uBAAnBA,qBAAA,CAAqBR,OAAO,OAAAS,qBAAA,GAC/Cd,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAe,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBd,KAAK,cAAAe,qBAAA,uBAA1BA,qBAAA,CAA4BV,OAAO,KACnC,cAAcL,KAAK,CAACH,QAAQ,CAACS,MAAM,MAAMN,KAAK,CAACH,QAAQ,CAACmB,UAAU,EAAE;QACtE,MAAM,IAAIT,KAAK,CAACH,YAAY,CAAC;MAC/B,CAAC,MAAM,IAAIJ,KAAK,CAACQ,OAAO,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,iFAAiF,CAAC;MACpG,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAAC,iCAAiC,GAAGP,KAAK,CAACK,OAAO,CAAC;MACpE;IACF;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAMY,kBAAkBA,CAACC,OAAO,EAAE;IAChC,IAAI;MACFvB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEsB,OAAO,CAAC;MAC7D,MAAMrB,QAAQ,GAAG,MAAMN,SAAS,CAAC4B,IAAI,CAAC,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC;MACrDvB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MACpD,OAAOF,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACM,OAAO,IAAIR,QAAQ,CAACE,IAAI;IACrE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,IAAIA,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAAuB,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QAClB,MAAMlB,YAAY,GAAG,EAAAgB,qBAAA,GAAApB,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAqB,qBAAA,uBAAnBA,qBAAA,CAAqBf,OAAO,OAAAgB,qBAAA,GAC/CrB,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAsB,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBrB,KAAK,cAAAsB,qBAAA,uBAA1BA,qBAAA,CAA4BjB,OAAO,KACnC,sBAAsBL,KAAK,CAACH,QAAQ,CAACS,MAAM,EAAE;QAC/C,MAAM,IAAIC,KAAK,CAACH,YAAY,CAAC;MAC/B,CAAC,MAAM,IAAIJ,KAAK,CAACQ,OAAO,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,gDAAgD,CAAC;MACnE,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAAC,gBAAgB,GAAGP,KAAK,CAACK,OAAO,CAAC;MACnD;IACF;EACF;AACF,CAAC;AAED,eAAeZ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}