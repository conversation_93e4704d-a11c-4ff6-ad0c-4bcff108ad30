import React from 'react';

const ErrorAlert = ({ message, retryFn }) => {
  if (!message) return null;

  return (
    <div className="alert alert-danger d-flex justify-content-between align-items-center" role="alert">
      <div>
        <strong>Error:</strong> {message}
      </div>
      {retryFn && (
        <button className="btn btn-sm btn-outline-light" onClick={retryFn}>
          Retry
        </button>
      )}
    </div>
  );
};

export default ErrorAlert;
