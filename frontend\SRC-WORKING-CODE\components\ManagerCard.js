const ManagerCard = ({ manager }) => {
  if (!manager) return null;

  if (manager.experience === -1) {
    return (
      <div className="card mb-4 border-danger">
        <div className="card-header bg-danger text-white">
          <h5 className="card-title mb-0">Manager</h5>
        </div>
        <div className="card-body">
          <p className="text-danger">Error loading manager data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="card mb-4">
      <div className="card-header bg-primary text-white">
        <h5 className="card-title mb-0">Manager</h5>
      </div>
      <div className="card-body">
        <p><strong>Name:</strong> {manager.name}</p>
        <p><strong>Experience:</strong> {manager.experience}</p>
      </div>
    </div>
  );
};

export default ManagerCard;
