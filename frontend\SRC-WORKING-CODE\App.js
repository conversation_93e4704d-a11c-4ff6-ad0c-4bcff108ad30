import React, { useState, useEffect } from 'react';
import EmployeeService from './services/EmployeeService';
import EmployeeCard from './components/EmployeeCard';
import DepartmentCard from './components/DepartmentCard';
import ManagerCard from './components/ManagerCard';
import ErrorAlert from './components/ErrorAlert';
import LoadingSpinner from './components/LoadingSpinner';

function App() {
  const [data, setData] = useState([]); // initialize as array
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    deptName: '',
    deptCode: '',
    empName: '',
    employeeId: '',
    managerName: '',
    experience: ''
  });
  const [submitMessage, setSubmitMessage] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await EmployeeService.getEmployeeData(); // expecting array of maps
      setData(result);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.message || 'Failed to fetch employee data');
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setSubmitMessage('');
      await EmployeeService.insertEmployeeData({
        deptName: formData.deptName,
        deptCode: formData.deptCode,
        empName: formData.empName,
        employeeId: parseInt(formData.employeeId),
        managerName: formData.managerName,
        experience: parseInt(formData.experience)
      });
      setSubmitMessage('Inserted successfully!');
      setFormData({
        deptName: '',
        deptCode: '',
        empName: '',
        employeeId: '',
        managerName: '',
        experience: ''
      });
      fetchData(); // refresh display
    } catch (err) {
      console.error(err);
      setSubmitMessage('Failed to insert: ' + (err.response?.data?.message || err.message));
    }
  };

  return (
    <div className="container mt-5">

      <h4>Add New Employee</h4>
      <form onSubmit={handleSubmit}>
        <div className="row mb-2">
          <div className="col">
            <input type="text" className="form-control" name="deptName" placeholder="Department Name" value={formData.deptName} onChange={handleInputChange} required />
          </div>
          <div className="col">
            <input type="text" className="form-control" name="deptCode" placeholder="Department Code" value={formData.deptCode} onChange={handleInputChange} required />
          </div>
        </div>

        <div className="row mb-2">
          <div className="col">
            <input type="text" className="form-control" name="empName" placeholder="Employee Name" value={formData.empName} onChange={handleInputChange} required />
          </div>
          <div className="col">
            <input type="number" className="form-control" name="employeeId" placeholder="Employee ID" value={formData.employeeId} onChange={handleInputChange} required />
          </div>
        </div>

        <div className="row mb-2">
          <div className="col">
            <input type="text" className="form-control" name="managerName" placeholder="Manager Name" value={formData.managerName} onChange={handleInputChange} required />
          </div>
          <div className="col">
            <input type="number" className="form-control" name="experience" placeholder="Manager Experience" value={formData.experience} onChange={handleInputChange} required />
          </div>
        </div>

        <button type="submit" className="btn btn-success mt-2">Submit</button>
        {submitMessage && <div className="mt-2">{submitMessage}</div>}
      </form>

      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Employee Dashboard</h1>
        <button
          className="btn btn-primary"
          onClick={fetchData}
          disabled={loading}
        >
          {loading ? 'Refreshing...' : 'Refresh Data'}
        </button>
      </div>

      {loading ? (
        <LoadingSpinner />
      ) : error ? (
        <ErrorAlert message={error} retryFn={fetchData} />
      ) : (
        <>
          {Array.isArray(data) && data.length > 0 ? (
            data.map((row, idx) => (
              <div className="row mb-4" key={idx}>
                <div className="col-md-4">
                  <EmployeeCard employee={row.employee} />
                </div>
                <div className="col-md-4">
                  <DepartmentCard department={row.department} />
                </div>
                <div className="col-md-4">
                  <ManagerCard manager={row.manager} />
                </div>
              </div>
            ))
          ) : (
            <div>No records found.</div>
          )}
        </>
      )}
    </div>
  );
}

export default App;
