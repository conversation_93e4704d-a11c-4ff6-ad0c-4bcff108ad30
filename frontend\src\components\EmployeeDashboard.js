import React, { useState, useEffect } from 'react';
import EmployeeService from '../services/EmployeeService';
import AuthService from '../services/AuthService';
import EmployeeCard from './EmployeeCard';
import DepartmentCard from './DepartmentCard';
import ManagerCard from './ManagerCard';
import ErrorAlert from './ErrorAlert';
import LoadingSpinner from './LoadingSpinner';

function EmployeeDashboard() {
  const [data, setData] = useState([]); // list of combined records
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [user, setUser] = useState(null);

  const [formData, setFormData] = useState({
    deptName: '',
    deptCode: '',
    empName: '',
    employeeId: '',
    managerName: '',
    experience: ''
  });

  const [submitMessage, setSubmitMessage] = useState('');
  const [submitLoading, setSubmitLoading] = useState(false);
  const [searchedEmployeeId, setSearchedEmployeeId] = useState(null);

  useEffect(() => {
    // Get current user
    const userData = AuthService.getCurrentUserData();
    setUser(userData);
    
    // Load initial data
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await EmployeeService.getEmployeeData();
      setData(result);
      setSearchedEmployeeId(null); // Reset search state when loading all data
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.message || 'Failed to fetch employee data');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitLoading(true);
    setSubmitMessage('');

    try {
      await EmployeeService.insertEmployeeData(formData);
      setSubmitMessage('Employee data inserted successfully!');
      
      // Reset form
      setFormData({
        deptName: '',
        deptCode: '',
        empName: '',
        employeeId: '',
        managerName: '',
        experience: ''
      });

      // Refresh data to show new employee
      await fetchData();
    } catch (err) {
      console.error('Error inserting data:', err);
      setSubmitMessage(`Error: ${err.message || 'Failed to insert employee data'}`);
    } finally {
      setSubmitLoading(false);
    }
  };

  const handleSearchById = async (employeeId) => {
    if (!employeeId.trim()) {
      await fetchData(); // Load all data if search is empty
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const result = await EmployeeService.getEmployeeDataById(employeeId);
      setData(Array.isArray(result) ? result : [result]);
      setSearchedEmployeeId(employeeId);
    } catch (err) {
      console.error('Error searching employee:', err);
      setError(err.message || 'Failed to search employee');
      setData([]);
      setSearchedEmployeeId(employeeId);
    } finally {
      setLoading(false);
    }
  };

  const clearSearch = () => {
    setSearchedEmployeeId(null);
    fetchData();
  };

  if (loading && data.length === 0) {
    return <LoadingSpinner message="Loading employee data..." />;
  }

  return (
    <div className="container mt-4">
      {/* Welcome Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card bg-primary text-white">
            <div className="card-body">
              <h1 className="card-title mb-2">
                <i className="bi bi-building me-2"></i>
                Employee Management Dashboard
              </h1>
              <p className="card-text mb-0">
                Welcome back, <strong>{user?.username}</strong>! 
                {user?.role === 'admin' ? ' You have administrator privileges.' : ' You have user access.'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <ErrorAlert 
          message={error} 
          onClose={() => setError(null)} 
        />
      )}

      {/* Search Section */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h5 className="card-title mb-0">
                <i className="bi bi-search me-2"></i>
                Search Employee
              </h5>
            </div>
            <div className="card-body">
              <SearchEmployeeForm 
                onSearch={handleSearchById}
                loading={loading}
              />
              {searchedEmployeeId && (
                <div className="mt-3">
                  <div className="d-flex align-items-center justify-content-between">
                    <small className="text-muted">
                      <i className="bi bi-info-circle me-1"></i>
                      Showing data for Employee ID: <strong>{searchedEmployeeId}</strong>
                    </small>
                    <button 
                      className="btn btn-sm btn-outline-secondary"
                      onClick={clearSearch}
                    >
                      <i className="bi bi-x-circle me-1"></i>
                      Show All Employees
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Add Employee Form - Admin Only */}
      {user?.role === 'admin' && (
        <div className="row mb-4">
          <div className="col-12">
            <div className="card">
              <div className="card-header">
                <h5 className="card-title mb-0">
                  <i className="bi bi-person-plus me-2"></i>
                  Add New Employee
                </h5>
              </div>
              <div className="card-body">
                <AddEmployeeForm
                  formData={formData}
                  onInputChange={handleInputChange}
                  onSubmit={handleSubmit}
                  submitMessage={submitMessage}
                  submitLoading={submitLoading}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Employee Data Display */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card">
            <div className="card-header d-flex justify-content-between align-items-center">
              <h5 className="card-title mb-0">
                <i className="bi bi-people me-2"></i>
                Employee Data
                {!searchedEmployeeId && (
                  <span className="badge bg-primary ms-2">{data.length} records</span>
                )}
              </h5>
              <button 
                className="btn btn-outline-primary btn-sm"
                onClick={fetchData}
                disabled={loading}
              >
                <i className="bi bi-arrow-clockwise me-1"></i>
                Refresh
              </button>
            </div>
            <div className="card-body">
              {loading ? (
                <div className="text-center py-4">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  <div className="mt-2">
                    <small className="text-muted">Loading employee data...</small>
                  </div>
                </div>
              ) : data.length === 0 ? (
                <div className="text-center py-4">
                  <i className="bi bi-inbox text-muted" style={{ fontSize: '3rem' }}></i>
                  <h5 className="text-muted mt-2">No Employee Data Found</h5>
                  <p className="text-muted">
                    {searchedEmployeeId 
                      ? `No employee found with ID: ${searchedEmployeeId}`
                      : 'No employee records available.'
                    }
                  </p>
                </div>
              ) : (
                <div className="row">
                  {data.map((item, index) => (
                    <div key={index} className="col-md-4 mb-3">
                      <div className="card h-100 border-0 shadow-sm">
                        <div className="card-body">
                          <EmployeeCard employee={item.employee} />
                          <hr />
                          <DepartmentCard department={item.department} />
                          <hr />
                          <ManagerCard manager={item.manager} />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Search Employee Form Component
const SearchEmployeeForm = ({ onSearch, loading }) => {
  const [searchId, setSearchId] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    onSearch(searchId);
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="row">
        <div className="col-md-8">
          <div className="input-group">
            <span className="input-group-text">
              <i className="bi bi-search"></i>
            </span>
            <input
              type="text"
              className="form-control"
              placeholder="Enter Employee ID to search..."
              value={searchId}
              onChange={(e) => setSearchId(e.target.value)}
              disabled={loading}
            />
          </div>
        </div>
        <div className="col-md-4">
          <button 
            type="submit" 
            className="btn btn-primary w-100"
            disabled={loading}
          >
            {loading ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Searching...
              </>
            ) : (
              <>
                <i className="bi bi-search me-2"></i>
                Search Employee
              </>
            )}
          </button>
        </div>
      </div>
    </form>
  );
};

// Add Employee Form Component
const AddEmployeeForm = ({ formData, onInputChange, onSubmit, submitMessage, submitLoading }) => {
  return (
    <form onSubmit={onSubmit}>
      <div className="row">
        <div className="col-md-6 mb-3">
          <label htmlFor="deptName" className="form-label">Department Name</label>
          <input
            type="text"
            className="form-control"
            id="deptName"
            name="deptName"
            value={formData.deptName}
            onChange={onInputChange}
            required
            disabled={submitLoading}
          />
        </div>
        <div className="col-md-6 mb-3">
          <label htmlFor="deptCode" className="form-label">Department Code</label>
          <input
            type="text"
            className="form-control"
            id="deptCode"
            name="deptCode"
            value={formData.deptCode}
            onChange={onInputChange}
            required
            disabled={submitLoading}
          />
        </div>
        <div className="col-md-6 mb-3">
          <label htmlFor="empName" className="form-label">Employee Name</label>
          <input
            type="text"
            className="form-control"
            id="empName"
            name="empName"
            value={formData.empName}
            onChange={onInputChange}
            required
            disabled={submitLoading}
          />
        </div>
        <div className="col-md-6 mb-3">
          <label htmlFor="employeeId" className="form-label">Employee ID</label>
          <input
            type="text"
            className="form-control"
            id="employeeId"
            name="employeeId"
            value={formData.employeeId}
            onChange={onInputChange}
            required
            disabled={submitLoading}
          />
        </div>
        <div className="col-md-6 mb-3">
          <label htmlFor="managerName" className="form-label">Manager Name</label>
          <input
            type="text"
            className="form-control"
            id="managerName"
            name="managerName"
            value={formData.managerName}
            onChange={onInputChange}
            required
            disabled={submitLoading}
          />
        </div>
        <div className="col-md-6 mb-3">
          <label htmlFor="experience" className="form-label">Experience (years)</label>
          <input
            type="number"
            className="form-control"
            id="experience"
            name="experience"
            value={formData.experience}
            onChange={onInputChange}
            required
            disabled={submitLoading}
          />
        </div>
      </div>
      
      <div className="d-grid">
        <button 
          type="submit" 
          className="btn btn-success"
          disabled={submitLoading}
        >
          {submitLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Adding Employee...
            </>
          ) : (
            <>
              <i className="bi bi-person-plus me-2"></i>
              Add Employee
            </>
          )}
        </button>
      </div>

      {submitMessage && (
        <div className={`alert mt-3 ${submitMessage.includes('Error') ? 'alert-danger' : 'alert-success'}`}>
          {submitMessage}
        </div>
      )}
    </form>
  );
};

export default EmployeeDashboard;
