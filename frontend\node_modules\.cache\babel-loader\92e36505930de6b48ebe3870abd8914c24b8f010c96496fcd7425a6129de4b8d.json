{"ast": null, "code": "import AuthService from './AuthService';\n\n// Base URL for BFF API calls\nconst BFF_BASE_URL = 'http://localhost:4000/api/employees'; // Node.js BFF endpoint\n\n// Create authenticated axios instance using AuthService\nconst apiClient = AuthService.createAuthenticatedClient(BFF_BASE_URL);\n\n// Service methods\nconst EmployeeService = {\n  /**\r\n   * Transform flat data structure to nested structure expected by components\r\n   * @param {Object} flatData - Flat data from backend\r\n   * @returns {Object} - Nested data structure\r\n   */\n  transformData(flatData) {\n    return {\n      employee: {\n        employeeId: flatData.employeeId,\n        name: flatData.empName\n      },\n      department: {\n        name: flatData.deptName,\n        code: flatData.deptCode\n      },\n      manager: {\n        name: flatData.managerName,\n        experience: flatData.experience\n      }\n    };\n  },\n  /**\r\n   * Fetches all employee, department, and manager data via BFF.\r\n   * @returns {Promise<Object[]>}\r\n   */\n  async getEmployeeData() {\n    try {\n      console.log('🔄 Fetching all employee data via BFF...');\n      const response = await apiClient.get('/'); // BFF endpoint for all employees\n      console.log('✅ BFF Response received:', response.data);\n      const rawData = response.data.data || response.data;\n\n      // Transform flat data structure to nested structure\n      if (Array.isArray(rawData)) {\n        return rawData.map(item => this.transformData(item));\n      } else {\n        return [this.transformData(rawData)];\n      }\n    } catch (error) {\n      console.error('❌ Error fetching employee data via BFF:', error);\n      if (error.response) {\n        var _error$response$data, _error$response$data2, _error$response$data3;\n        const errorMessage = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : (_error$response$data3 = _error$response$data2.error) === null || _error$response$data3 === void 0 ? void 0 : _error$response$data3.message) || `BFF error: ${error.response.status}`;\n        throw new Error(errorMessage);\n      } else if (error.request) {\n        throw new Error('No response from BFF server. Please check your connection.');\n      } else {\n        throw new Error('Error: ' + error.message);\n      }\n    }\n  },\n  /**\r\n   * Fetches data for a specific employee ID via BFF.\r\n   * @param {number} employeeId\r\n   * @returns {Promise<Object[]>}\r\n   */\n  async getEmployeeDataById(employeeId) {\n    try {\n      console.log(`🔄 Fetching employee data for ID: ${employeeId} via BFF...`);\n      const response = await apiClient.get(`/${employeeId}`); // BFF endpoint for specific employee\n      console.log('✅ BFF Response received:', response.data);\n      const data = response.data.data || response.data;\n      return data;\n    } catch (error) {\n      console.error('❌ Error in getEmployeeDataById via BFF:', error);\n      if (error.response) {\n        var _error$response$data4, _error$response$data5, _error$response$data6;\n        const errorMessage = ((_error$response$data4 = error.response.data) === null || _error$response$data4 === void 0 ? void 0 : _error$response$data4.message) || ((_error$response$data5 = error.response.data) === null || _error$response$data5 === void 0 ? void 0 : (_error$response$data6 = _error$response$data5.error) === null || _error$response$data6 === void 0 ? void 0 : _error$response$data6.message) || `BFF error: ${error.response.status} - ${error.response.statusText}`;\n        throw new Error(errorMessage);\n      } else if (error.request) {\n        throw new Error('No response from BFF server. Please check if the BFF is running and accessible.');\n      } else {\n        throw new Error('Error fetching employee by ID: ' + error.message);\n      }\n    }\n  },\n  /**\r\n   * Inserts new employee, department, and manager records via BFF.\r\n   * @param {Object} payload\r\n   * @returns {Promise<string>}\r\n   */\n  async insertEmployeeData(payload) {\n    try {\n      console.log('🔄 Inserting employee data via BFF...', payload);\n      const response = await apiClient.post('/', payload); // BFF endpoint for insert\n      console.log('✅ BFF Insert response:', response.data);\n      return response.data.data || response.data.message || response.data;\n    } catch (error) {\n      console.error('❌ Error inserting employee data via BFF:', error);\n      if (error.response) {\n        var _error$response$data7, _error$response$data8, _error$response$data9;\n        const errorMessage = ((_error$response$data7 = error.response.data) === null || _error$response$data7 === void 0 ? void 0 : _error$response$data7.message) || ((_error$response$data8 = error.response.data) === null || _error$response$data8 === void 0 ? void 0 : (_error$response$data9 = _error$response$data8.error) === null || _error$response$data9 === void 0 ? void 0 : _error$response$data9.message) || `BFF insert failed: ${error.response.status}`;\n        throw new Error(errorMessage);\n      } else if (error.request) {\n        throw new Error('Insert request failed. No BFF server response.');\n      } else {\n        throw new Error('Insert error: ' + error.message);\n      }\n    }\n  }\n};\nexport default EmployeeService;", "map": {"version": 3, "names": ["AuthService", "BFF_BASE_URL", "apiClient", "createAuthenticatedClient", "EmployeeService", "transformData", "flatData", "employee", "employeeId", "name", "empName", "department", "deptName", "code", "deptCode", "manager", "<PERSON><PERSON><PERSON>", "experience", "getEmployeeData", "console", "log", "response", "get", "data", "rawData", "Array", "isArray", "map", "item", "error", "_error$response$data", "_error$response$data2", "_error$response$data3", "errorMessage", "message", "status", "Error", "request", "getEmployeeDataById", "_error$response$data4", "_error$response$data5", "_error$response$data6", "statusText", "insertEmployeeData", "payload", "post", "_error$response$data7", "_error$response$data8", "_error$response$data9"], "sources": ["D:/FRONT-END-BACK-END-COMMUNICATION/frontend/src/services/EmployeeService.js"], "sourcesContent": ["import AuthService from './AuthService';\r\n\r\n// Base URL for BFF API calls\r\nconst BFF_BASE_URL = 'http://localhost:4000/api/employees'; // Node.js BFF endpoint\r\n\r\n// Create authenticated axios instance using AuthService\r\nconst apiClient = AuthService.createAuthenticatedClient(BFF_BASE_URL);\r\n\r\n// Service methods\r\nconst EmployeeService = {\r\n  /**\r\n   * Transform flat data structure to nested structure expected by components\r\n   * @param {Object} flatData - Flat data from backend\r\n   * @returns {Object} - Nested data structure\r\n   */\r\n  transformData(flatData) {\r\n    return {\r\n      employee: {\r\n        employeeId: flatData.employeeId,\r\n        name: flatData.empName\r\n      },\r\n      department: {\r\n        name: flatData.deptName,\r\n        code: flatData.deptCode\r\n      },\r\n      manager: {\r\n        name: flatData.managerName,\r\n        experience: flatData.experience\r\n      }\r\n    };\r\n  },\r\n\r\n  /**\r\n   * Fetches all employee, department, and manager data via BFF.\r\n   * @returns {Promise<Object[]>}\r\n   */\r\n  async getEmployeeData() {\r\n    try {\r\n      console.log('🔄 Fetching all employee data via BFF...');\r\n      const response = await apiClient.get('/'); // BFF endpoint for all employees\r\n      console.log('✅ BFF Response received:', response.data);\r\n\r\n      const rawData = response.data.data || response.data;\r\n\r\n      // Transform flat data structure to nested structure\r\n      if (Array.isArray(rawData)) {\r\n        return rawData.map(item => this.transformData(item));\r\n      } else {\r\n        return [this.transformData(rawData)];\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error fetching employee data via BFF:', error);\r\n      if (error.response) {\r\n        const errorMessage = error.response.data?.message ||\r\n          error.response.data?.error?.message ||\r\n          `BFF error: ${error.response.status}`;\r\n        throw new Error(errorMessage);\r\n      } else if (error.request) {\r\n        throw new Error('No response from BFF server. Please check your connection.');\r\n      } else {\r\n        throw new Error('Error: ' + error.message);\r\n      }\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Fetches data for a specific employee ID via BFF.\r\n   * @param {number} employeeId\r\n   * @returns {Promise<Object[]>}\r\n   */\r\n  async getEmployeeDataById(employeeId) {\r\n    try {\r\n      console.log(`🔄 Fetching employee data for ID: ${employeeId} via BFF...`);\r\n      const response = await apiClient.get(`/${employeeId}`); // BFF endpoint for specific employee\r\n      console.log('✅ BFF Response received:', response.data);\r\n\r\n      const data = response.data.data || response.data;\r\n      return data;\r\n    } catch (error) {\r\n      console.error('❌ Error in getEmployeeDataById via BFF:', error);\r\n      if (error.response) {\r\n        const errorMessage = error.response.data?.message ||\r\n          error.response.data?.error?.message ||\r\n          `BFF error: ${error.response.status} - ${error.response.statusText}`;\r\n        throw new Error(errorMessage);\r\n      } else if (error.request) {\r\n        throw new Error('No response from BFF server. Please check if the BFF is running and accessible.');\r\n      } else {\r\n        throw new Error('Error fetching employee by ID: ' + error.message);\r\n      }\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Inserts new employee, department, and manager records via BFF.\r\n   * @param {Object} payload\r\n   * @returns {Promise<string>}\r\n   */\r\n  async insertEmployeeData(payload) {\r\n    try {\r\n      console.log('🔄 Inserting employee data via BFF...', payload);\r\n      const response = await apiClient.post('/', payload); // BFF endpoint for insert\r\n      console.log('✅ BFF Insert response:', response.data);\r\n      return response.data.data || response.data.message || response.data;\r\n    } catch (error) {\r\n      console.error('❌ Error inserting employee data via BFF:', error);\r\n      if (error.response) {\r\n        const errorMessage = error.response.data?.message ||\r\n          error.response.data?.error?.message ||\r\n          `BFF insert failed: ${error.response.status}`;\r\n        throw new Error(errorMessage);\r\n      } else if (error.request) {\r\n        throw new Error('Insert request failed. No BFF server response.');\r\n      } else {\r\n        throw new Error('Insert error: ' + error.message);\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nexport default EmployeeService;\r\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;;AAEvC;AACA,MAAMC,YAAY,GAAG,qCAAqC,CAAC,CAAC;;AAE5D;AACA,MAAMC,SAAS,GAAGF,WAAW,CAACG,yBAAyB,CAACF,YAAY,CAAC;;AAErE;AACA,MAAMG,eAAe,GAAG;EACtB;AACF;AACA;AACA;AACA;EACEC,aAAaA,CAACC,QAAQ,EAAE;IACtB,OAAO;MACLC,QAAQ,EAAE;QACRC,UAAU,EAAEF,QAAQ,CAACE,UAAU;QAC/BC,IAAI,EAAEH,QAAQ,CAACI;MACjB,CAAC;MACDC,UAAU,EAAE;QACVF,IAAI,EAAEH,QAAQ,CAACM,QAAQ;QACvBC,IAAI,EAAEP,QAAQ,CAACQ;MACjB,CAAC;MACDC,OAAO,EAAE;QACPN,IAAI,EAAEH,QAAQ,CAACU,WAAW;QAC1BC,UAAU,EAAEX,QAAQ,CAACW;MACvB;IACF,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;EACE,MAAMC,eAAeA,CAAA,EAAG;IACtB,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,MAAMC,QAAQ,GAAG,MAAMnB,SAAS,CAACoB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3CH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAEtD,MAAMC,OAAO,GAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI;;MAEnD;MACA,IAAIE,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;QAC1B,OAAOA,OAAO,CAACG,GAAG,CAACC,IAAI,IAAI,IAAI,CAACvB,aAAa,CAACuB,IAAI,CAAC,CAAC;MACtD,CAAC,MAAM;QACL,OAAO,CAAC,IAAI,CAACvB,aAAa,CAACmB,OAAO,CAAC,CAAC;MACtC;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAIA,KAAK,CAACR,QAAQ,EAAE;QAAA,IAAAS,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QAClB,MAAMC,YAAY,GAAG,EAAAH,oBAAA,GAAAD,KAAK,CAACR,QAAQ,CAACE,IAAI,cAAAO,oBAAA,uBAAnBA,oBAAA,CAAqBI,OAAO,OAAAH,qBAAA,GAC/CF,KAAK,CAACR,QAAQ,CAACE,IAAI,cAAAQ,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBF,KAAK,cAAAG,qBAAA,uBAA1BA,qBAAA,CAA4BE,OAAO,KACnC,cAAcL,KAAK,CAACR,QAAQ,CAACc,MAAM,EAAE;QACvC,MAAM,IAAIC,KAAK,CAACH,YAAY,CAAC;MAC/B,CAAC,MAAM,IAAIJ,KAAK,CAACQ,OAAO,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,4DAA4D,CAAC;MAC/E,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAAC,SAAS,GAAGP,KAAK,CAACK,OAAO,CAAC;MAC5C;IACF;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAMI,mBAAmBA,CAAC9B,UAAU,EAAE;IACpC,IAAI;MACFW,OAAO,CAACC,GAAG,CAAC,qCAAqCZ,UAAU,aAAa,CAAC;MACzE,MAAMa,QAAQ,GAAG,MAAMnB,SAAS,CAACoB,GAAG,CAAC,IAAId,UAAU,EAAE,CAAC,CAAC,CAAC;MACxDW,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAEtD,MAAMA,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI;MAChD,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAIA,KAAK,CAACR,QAAQ,EAAE;QAAA,IAAAkB,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QAClB,MAAMR,YAAY,GAAG,EAAAM,qBAAA,GAAAV,KAAK,CAACR,QAAQ,CAACE,IAAI,cAAAgB,qBAAA,uBAAnBA,qBAAA,CAAqBL,OAAO,OAAAM,qBAAA,GAC/CX,KAAK,CAACR,QAAQ,CAACE,IAAI,cAAAiB,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBX,KAAK,cAAAY,qBAAA,uBAA1BA,qBAAA,CAA4BP,OAAO,KACnC,cAAcL,KAAK,CAACR,QAAQ,CAACc,MAAM,MAAMN,KAAK,CAACR,QAAQ,CAACqB,UAAU,EAAE;QACtE,MAAM,IAAIN,KAAK,CAACH,YAAY,CAAC;MAC/B,CAAC,MAAM,IAAIJ,KAAK,CAACQ,OAAO,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,iFAAiF,CAAC;MACpG,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAAC,iCAAiC,GAAGP,KAAK,CAACK,OAAO,CAAC;MACpE;IACF;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAMS,kBAAkBA,CAACC,OAAO,EAAE;IAChC,IAAI;MACFzB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEwB,OAAO,CAAC;MAC7D,MAAMvB,QAAQ,GAAG,MAAMnB,SAAS,CAAC2C,IAAI,CAAC,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC;MACrDzB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MACpD,OAAOF,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACW,OAAO,IAAIb,QAAQ,CAACE,IAAI;IACrE,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,IAAIA,KAAK,CAACR,QAAQ,EAAE;QAAA,IAAAyB,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QAClB,MAAMf,YAAY,GAAG,EAAAa,qBAAA,GAAAjB,KAAK,CAACR,QAAQ,CAACE,IAAI,cAAAuB,qBAAA,uBAAnBA,qBAAA,CAAqBZ,OAAO,OAAAa,qBAAA,GAC/ClB,KAAK,CAACR,QAAQ,CAACE,IAAI,cAAAwB,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBlB,KAAK,cAAAmB,qBAAA,uBAA1BA,qBAAA,CAA4Bd,OAAO,KACnC,sBAAsBL,KAAK,CAACR,QAAQ,CAACc,MAAM,EAAE;QAC/C,MAAM,IAAIC,KAAK,CAACH,YAAY,CAAC;MAC/B,CAAC,MAAM,IAAIJ,KAAK,CAACQ,OAAO,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,gDAAgD,CAAC;MACnE,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAAC,gBAAgB,GAAGP,KAAK,CAACK,OAAO,CAAC;MACnD;IACF;EACF;AACF,CAAC;AAED,eAAe9B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}