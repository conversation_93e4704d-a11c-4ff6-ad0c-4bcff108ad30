{"ast": null, "code": "var _jsxFileName = \"D:\\\\FRONT-END-BACK-END-COMMUNICATION\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport AuthService from '../services/AuthService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requiredRole = null,\n  adminOnly = false\n}) => {\n  _s();\n  const [isAuthenticated, setIsAuthenticated] = useState(null); // null = checking, true/false = result\n  const [userRole, setUserRole] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const location = useLocation();\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n  const checkAuthentication = async () => {\n    try {\n      // Check if user has valid tokens\n      if (!AuthService.isAuthenticated()) {\n        setIsAuthenticated(false);\n        setLoading(false);\n        return;\n      }\n\n      // Verify token with server\n      const response = await AuthService.verifyToken();\n      if (response.data.valid) {\n        setIsAuthenticated(true);\n        setUserRole(response.data.user.role);\n      } else {\n        setIsAuthenticated(false);\n        AuthService.clearTokens(); // Clear invalid tokens\n      }\n    } catch (error) {\n      console.error('Authentication check failed:', error);\n      setIsAuthenticated(false);\n      AuthService.clearTokens(); // Clear invalid tokens\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center min-vh-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: \"Verifying authentication...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check role-based access\n  if (adminOnly && userRole !== 'admin') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mt-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card border-danger\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-body text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-shield-exclamation text-danger\",\n                style: {\n                  fontSize: '3rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"card-title text-danger mt-3\",\n                children: \"Access Denied\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"card-text\",\n                children: \"You don't have permission to access this page. Administrator privileges are required.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [\"Current role: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-secondary\",\n                    children: userRole\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 80,\n                    columnNumber: 35\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-outline-primary\",\n                  onClick: () => window.history.back(),\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bi bi-arrow-left me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 21\n                  }, this), \"Go Back\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Check specific required role\n  if (requiredRole && userRole !== requiredRole && userRole !== 'admin') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mt-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card border-warning\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-body text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-person-exclamation text-warning\",\n                style: {\n                  fontSize: '3rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"card-title text-warning mt-3\",\n                children: \"Insufficient Permissions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"card-text\",\n                children: \"You don't have the required role to access this page.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [\"Required role: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-primary\",\n                    children: requiredRole\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 36\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 92\n                  }, this), \"Your role: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-secondary\",\n                    children: userRole\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 116,\n                    columnNumber: 32\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-outline-primary\",\n                  onClick: () => window.history.back(),\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bi bi-arrow-left me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 21\n                  }, this), \"Go Back\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this);\n  }\n\n  // User is authenticated and has required permissions\n  return children;\n};\n_s(ProtectedRoute, \"fhLzpmFP5YnpWjvHuPE17RscylU=\", false, function () {\n  return [useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Navigate", "useLocation", "AuthService", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requiredRole", "adminOnly", "_s", "isAuthenticated", "setIsAuthenticated", "userRole", "setUserRole", "loading", "setLoading", "location", "checkAuthentication", "response", "verifyToken", "data", "valid", "user", "role", "clearTokens", "error", "console", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "style", "fontSize", "onClick", "window", "history", "back", "_c", "$RefreshReg$"], "sources": ["D:/FRONT-END-BACK-END-COMMUNICATION/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport AuthService from '../services/AuthService';\n\nconst ProtectedRoute = ({ children, requiredRole = null, adminOnly = false }) => {\n  const [isAuthenticated, setIsAuthenticated] = useState(null); // null = checking, true/false = result\n  const [userRole, setUserRole] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const location = useLocation();\n\n  useEffect(() => {\n    checkAuthentication();\n  }, []);\n\n  const checkAuthentication = async () => {\n    try {\n      // Check if user has valid tokens\n      if (!AuthService.isAuthenticated()) {\n        setIsAuthenticated(false);\n        setLoading(false);\n        return;\n      }\n\n      // Verify token with server\n      const response = await AuthService.verifyToken();\n      \n      if (response.data.valid) {\n        setIsAuthenticated(true);\n        setUserRole(response.data.user.role);\n      } else {\n        setIsAuthenticated(false);\n        AuthService.clearTokens(); // Clear invalid tokens\n      }\n    } catch (error) {\n      console.error('Authentication check failed:', error);\n      setIsAuthenticated(false);\n      AuthService.clearTokens(); // Clear invalid tokens\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className=\"d-flex justify-content-center align-items-center min-vh-100\">\n        <div className=\"text-center\">\n          <div className=\"spinner-border text-primary\" role=\"status\">\n            <span className=\"visually-hidden\">Loading...</span>\n          </div>\n          <div className=\"mt-2\">\n            <small className=\"text-muted\">Verifying authentication...</small>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // Check role-based access\n  if (adminOnly && userRole !== 'admin') {\n    return (\n      <div className=\"container mt-5\">\n        <div className=\"row justify-content-center\">\n          <div className=\"col-md-6\">\n            <div className=\"card border-danger\">\n              <div className=\"card-body text-center\">\n                <i className=\"bi bi-shield-exclamation text-danger\" style={{ fontSize: '3rem' }}></i>\n                <h4 className=\"card-title text-danger mt-3\">Access Denied</h4>\n                <p className=\"card-text\">\n                  You don't have permission to access this page. \n                  Administrator privileges are required.\n                </p>\n                <div className=\"mt-3\">\n                  <small className=\"text-muted\">\n                    Current role: <span className=\"badge bg-secondary\">{userRole}</span>\n                  </small>\n                </div>\n                <div className=\"mt-3\">\n                  <button \n                    className=\"btn btn-outline-primary\"\n                    onClick={() => window.history.back()}\n                  >\n                    <i className=\"bi bi-arrow-left me-1\"></i>\n                    Go Back\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Check specific required role\n  if (requiredRole && userRole !== requiredRole && userRole !== 'admin') {\n    return (\n      <div className=\"container mt-5\">\n        <div className=\"row justify-content-center\">\n          <div className=\"col-md-6\">\n            <div className=\"card border-warning\">\n              <div className=\"card-body text-center\">\n                <i className=\"bi bi-person-exclamation text-warning\" style={{ fontSize: '3rem' }}></i>\n                <h4 className=\"card-title text-warning mt-3\">Insufficient Permissions</h4>\n                <p className=\"card-text\">\n                  You don't have the required role to access this page.\n                </p>\n                <div className=\"mt-3\">\n                  <small className=\"text-muted\">\n                    Required role: <span className=\"badge bg-primary\">{requiredRole}</span><br />\n                    Your role: <span className=\"badge bg-secondary\">{userRole}</span>\n                  </small>\n                </div>\n                <div className=\"mt-3\">\n                  <button \n                    className=\"btn btn-outline-primary\"\n                    onClick={() => window.history.back()}\n                  >\n                    <i className=\"bi bi-arrow-left me-1\"></i>\n                    Go Back\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // User is authenticated and has required permissions\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,YAAY,GAAG,IAAI;EAAEC,SAAS,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMkB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACdkB,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF;MACA,IAAI,CAACf,WAAW,CAACQ,eAAe,CAAC,CAAC,EAAE;QAClCC,kBAAkB,CAAC,KAAK,CAAC;QACzBI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMG,QAAQ,GAAG,MAAMhB,WAAW,CAACiB,WAAW,CAAC,CAAC;MAEhD,IAAID,QAAQ,CAACE,IAAI,CAACC,KAAK,EAAE;QACvBV,kBAAkB,CAAC,IAAI,CAAC;QACxBE,WAAW,CAACK,QAAQ,CAACE,IAAI,CAACE,IAAI,CAACC,IAAI,CAAC;MACtC,CAAC,MAAM;QACLZ,kBAAkB,CAAC,KAAK,CAAC;QACzBT,WAAW,CAACsB,WAAW,CAAC,CAAC,CAAC,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDd,kBAAkB,CAAC,KAAK,CAAC;MACzBT,WAAW,CAACsB,WAAW,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,IAAID,OAAO,EAAE;IACX,oBACEV,OAAA;MAAKuB,SAAS,EAAC,6DAA6D;MAAArB,QAAA,eAC1EF,OAAA;QAAKuB,SAAS,EAAC,aAAa;QAAArB,QAAA,gBAC1BF,OAAA;UAAKuB,SAAS,EAAC,6BAA6B;UAACJ,IAAI,EAAC,QAAQ;UAAAjB,QAAA,eACxDF,OAAA;YAAMuB,SAAS,EAAC,iBAAiB;YAAArB,QAAA,EAAC;UAAU;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACN3B,OAAA;UAAKuB,SAAS,EAAC,MAAM;UAAArB,QAAA,eACnBF,OAAA;YAAOuB,SAAS,EAAC,YAAY;YAAArB,QAAA,EAAC;UAA2B;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACrB,eAAe,EAAE;IACpB,oBAAON,OAAA,CAACJ,QAAQ;MAACgC,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAElB;MAAS,CAAE;MAACmB,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;;EAEA;EACA,IAAIvB,SAAS,IAAII,QAAQ,KAAK,OAAO,EAAE;IACrC,oBACER,OAAA;MAAKuB,SAAS,EAAC,gBAAgB;MAAArB,QAAA,eAC7BF,OAAA;QAAKuB,SAAS,EAAC,4BAA4B;QAAArB,QAAA,eACzCF,OAAA;UAAKuB,SAAS,EAAC,UAAU;UAAArB,QAAA,eACvBF,OAAA;YAAKuB,SAAS,EAAC,oBAAoB;YAAArB,QAAA,eACjCF,OAAA;cAAKuB,SAAS,EAAC,uBAAuB;cAAArB,QAAA,gBACpCF,OAAA;gBAAGuB,SAAS,EAAC,sCAAsC;gBAACS,KAAK,EAAE;kBAAEC,QAAQ,EAAE;gBAAO;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrF3B,OAAA;gBAAIuB,SAAS,EAAC,6BAA6B;gBAAArB,QAAA,EAAC;cAAa;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9D3B,OAAA;gBAAGuB,SAAS,EAAC,WAAW;gBAAArB,QAAA,EAAC;cAGzB;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ3B,OAAA;gBAAKuB,SAAS,EAAC,MAAM;gBAAArB,QAAA,eACnBF,OAAA;kBAAOuB,SAAS,EAAC,YAAY;kBAAArB,QAAA,GAAC,gBACd,eAAAF,OAAA;oBAAMuB,SAAS,EAAC,oBAAoB;oBAAArB,QAAA,EAAEM;kBAAQ;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN3B,OAAA;gBAAKuB,SAAS,EAAC,MAAM;gBAAArB,QAAA,eACnBF,OAAA;kBACEuB,SAAS,EAAC,yBAAyB;kBACnCW,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;kBAAAnC,QAAA,gBAErCF,OAAA;oBAAGuB,SAAS,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,WAE3C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIxB,YAAY,IAAIK,QAAQ,KAAKL,YAAY,IAAIK,QAAQ,KAAK,OAAO,EAAE;IACrE,oBACER,OAAA;MAAKuB,SAAS,EAAC,gBAAgB;MAAArB,QAAA,eAC7BF,OAAA;QAAKuB,SAAS,EAAC,4BAA4B;QAAArB,QAAA,eACzCF,OAAA;UAAKuB,SAAS,EAAC,UAAU;UAAArB,QAAA,eACvBF,OAAA;YAAKuB,SAAS,EAAC,qBAAqB;YAAArB,QAAA,eAClCF,OAAA;cAAKuB,SAAS,EAAC,uBAAuB;cAAArB,QAAA,gBACpCF,OAAA;gBAAGuB,SAAS,EAAC,uCAAuC;gBAACS,KAAK,EAAE;kBAAEC,QAAQ,EAAE;gBAAO;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtF3B,OAAA;gBAAIuB,SAAS,EAAC,8BAA8B;gBAAArB,QAAA,EAAC;cAAwB;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1E3B,OAAA;gBAAGuB,SAAS,EAAC,WAAW;gBAAArB,QAAA,EAAC;cAEzB;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ3B,OAAA;gBAAKuB,SAAS,EAAC,MAAM;gBAAArB,QAAA,eACnBF,OAAA;kBAAOuB,SAAS,EAAC,YAAY;kBAAArB,QAAA,GAAC,iBACb,eAAAF,OAAA;oBAAMuB,SAAS,EAAC,kBAAkB;oBAAArB,QAAA,EAAEC;kBAAY;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAAA3B,OAAA;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClE,eAAA3B,OAAA;oBAAMuB,SAAS,EAAC,oBAAoB;oBAAArB,QAAA,EAAEM;kBAAQ;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN3B,OAAA;gBAAKuB,SAAS,EAAC,MAAM;gBAAArB,QAAA,eACnBF,OAAA;kBACEuB,SAAS,EAAC,yBAAyB;kBACnCW,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;kBAAAnC,QAAA,gBAErCF,OAAA;oBAAGuB,SAAS,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,WAE3C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,OAAOzB,QAAQ;AACjB,CAAC;AAACG,EAAA,CArIIJ,cAAc;EAAA,QAIDJ,WAAW;AAAA;AAAAyC,EAAA,GAJxBrC,cAAc;AAuIpB,eAAeA,cAAc;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}