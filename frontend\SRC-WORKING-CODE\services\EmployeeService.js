import axios from 'axios';

// Base URL for API calls
#const API_BASE_URL = 'http://localhost:8080/api'; // Ensure your backend path is correct
const API_BASE_URL = 'http://localhost:8080/employee-department-manager'; // Ensure your backend path is correct

// Axios instance with defaults
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 10000
});

// Service methods
const EmployeeService = {
  /**
   * Fetches employee, department, and manager data.
   * @returns {Promise<Object>} Flux: array of maps, or Mono: single map
   */
  async getEmployeeData() {
    try {
      const response = await apiClient.get('/employee-data'); // Make sure backend matches this path
      return response.data;
    } catch (error) {
      if (error.response) {
        const errorMessage = error.response.data?.message ||
          'Server error: ' + error.response.status;
        throw new Error(errorMessage);
      } else if (error.request) {
        throw new Error('No response from server. Please check your connection.');
      } else {
        throw new Error('Error: ' + error.message);
      }
    }
  },

  /**
   * Inserts a new employee, department, and manager.
   * @param {Object} payload - Combined insert object
   * @returns {Promise<String>} Response message
   */
  async insertEmployeeData(payload) {
    try {
      const response = await apiClient.post('/insert', payload); // Endpoint: POST /api/insert
      return response.data;
    } catch (error) {
      if (error.response) {
        const errorMessage = error.response.data?.message ||
          'Insert failed: ' + error.response.status;
        throw new Error(errorMessage);
      } else if (error.request) {
        throw new Error('Insert request failed. No server response.');
      } else {
        throw new Error('Insert error: ' + error.message);
      }
    }
  }
};

export default EmployeeService;
