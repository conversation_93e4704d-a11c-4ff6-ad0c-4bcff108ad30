{"ast": null, "code": "var _jsxFileName = \"D:\\\\FRONT-END-BACK-END-COMMUNICATION\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport AuthService from './services/AuthService';\nimport Login from './components/Login';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Header from './components/Header';\nimport EmployeeDashboard from './components/EmployeeDashboard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [user, setUser] = useState(null);\n  useEffect(() => {\n    // Check if user is already authenticated\n    const userData = AuthService.getCurrentUserData();\n    setUser(userData);\n  }, []);\n  const handleLogin = userData => {\n    setUser(userData);\n  };\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [AuthService.isAuthenticated() && /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 43\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {\n            onLogin: handleLogin\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 22\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(EmployeeDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 22\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"5s2qRsV95gTJBmaaTh11GoxYeGE=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "AuthService", "<PERSON><PERSON>", "ProtectedRoute", "Header", "EmployeeDashboard", "jsxDEV", "_jsxDEV", "App", "_s", "user", "setUser", "userData", "getCurrentUserData", "handleLogin", "children", "className", "isAuthenticated", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "onLogin", "to", "replace", "_c", "$RefreshReg$"], "sources": ["D:/FRONT-END-BACK-END-COMMUNICATION/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\r\nimport AuthService from './services/AuthService';\r\nimport Login from './components/Login';\r\nimport ProtectedRoute from './components/ProtectedRoute';\r\nimport Header from './components/Header';\r\nimport EmployeeDashboard from './components/EmployeeDashboard';\r\n\r\nfunction App() {\r\n  const [user, setUser] = useState(null);\r\n\r\n  useEffect(() => {\r\n    // Check if user is already authenticated\r\n    const userData = AuthService.getCurrentUserData();\r\n    setUser(userData);\r\n  }, []);\r\n\r\n  const handleLogin = (userData) => {\r\n    setUser(userData);\r\n  };\r\n\r\n  return (\r\n    <Router>\r\n      <div className=\"App\">\r\n        {/* Header - only show if authenticated */}\r\n        {AuthService.isAuthenticated() && <Header />}\r\n\r\n        <Routes>\r\n          {/* Login Route */}\r\n          <Route\r\n            path=\"/login\"\r\n            element={<Login onLogin={handleLogin} />}\r\n          />\r\n\r\n          {/* Protected Dashboard Route */}\r\n          <Route\r\n            path=\"/\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <EmployeeDashboard />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* Redirect any unknown routes to dashboard */}\r\n          <Route\r\n            path=\"*\"\r\n            element={<Navigate to=\"/\" replace />}\r\n          />\r\n        </Routes>\r\n      </div>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,iBAAiB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd;IACA,MAAMiB,QAAQ,GAAGX,WAAW,CAACY,kBAAkB,CAAC,CAAC;IACjDF,OAAO,CAACC,QAAQ,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,WAAW,GAAIF,QAAQ,IAAK;IAChCD,OAAO,CAACC,QAAQ,CAAC;EACnB,CAAC;EAED,oBACEL,OAAA,CAACV,MAAM;IAAAkB,QAAA,eACLR,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAD,QAAA,GAEjBd,WAAW,CAACgB,eAAe,CAAC,CAAC,iBAAIV,OAAA,CAACH,MAAM;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE5Cd,OAAA,CAACT,MAAM;QAAAiB,QAAA,gBAELR,OAAA,CAACR,KAAK;UACJuB,IAAI,EAAC,QAAQ;UACbC,OAAO,eAAEhB,OAAA,CAACL,KAAK;YAACsB,OAAO,EAAEV;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAGFd,OAAA,CAACR,KAAK;UACJuB,IAAI,EAAC,GAAG;UACRC,OAAO,eACLhB,OAAA,CAACJ,cAAc;YAAAY,QAAA,eACbR,OAAA,CAACF,iBAAiB;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGFd,OAAA,CAACR,KAAK;UACJuB,IAAI,EAAC,GAAG;UACRC,OAAO,eAAEhB,OAAA,CAACP,QAAQ;YAACyB,EAAE,EAAC,GAAG;YAACC,OAAO;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACZ,EAAA,CA7CQD,GAAG;AAAAmB,EAAA,GAAHnB,GAAG;AA+CZ,eAAeA,GAAG;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}