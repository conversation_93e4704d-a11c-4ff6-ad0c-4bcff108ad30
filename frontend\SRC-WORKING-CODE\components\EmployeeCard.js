import React from 'react';

const EmployeeCard = ({ employee }) => {
  if (!employee) return null;

  if (employee?.employeeId === -1) {
    return (
      <div className="card mb-4 border-danger">
        <div className="card-header bg-danger text-white">
          <h5 className="card-title mb-0">Employee</h5>
        </div>
        <div className="card-body">
          <p className="text-danger">Error loading employee data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="card mb-4">
      <div className="card-header bg-primary text-white">
        <h5 className="card-title mb-0">Employee</h5>
      </div>
      <div className="card-body">
        <p><strong>Name:</strong> {employee.name}</p>
        <p><strong>ID:</strong> {employee.employeeId}</p>
      </div>
    </div>
  );
};

export default EmployeeCard;
