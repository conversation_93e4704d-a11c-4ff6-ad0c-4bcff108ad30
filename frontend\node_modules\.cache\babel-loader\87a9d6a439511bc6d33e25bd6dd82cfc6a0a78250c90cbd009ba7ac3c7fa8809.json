{"ast": null, "code": "import axios from 'axios';\n\n// Base URL for BFF API calls\nconst BFF_BASE_URL = 'http://localhost:4000/api/employees'; // Node.js BFF endpoint\n\n// Axios instance with BFF configuration\nconst apiClient = axios.create({\n  baseURL: BFF_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 15000 // Increased timeout for BFF processing\n});\n\n// Service methods\nconst EmployeeService = {\n  /**\r\n   * Fetches all employee, department, and manager data.\r\n   * @returns {Promise<Object[]>}\r\n   */\n  async getEmployeeData() {\n    try {\n      const response = await apiClient.get('/employee-data-list');\n      return response.data;\n    } catch (error) {\n      if (error.response) {\n        var _error$response$data;\n        throw new Error(((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || `Server error: ${error.response.status}`);\n      } else if (error.request) {\n        throw new Error('No response from server. Please check your connection.');\n      } else {\n        throw new Error('Error: ' + error.message);\n      }\n    }\n  },\n  /**\r\n   * Fetches data for a specific employee ID.\r\n   * @param {number} employeeId\r\n   * @returns {Promise<Object[]>}\r\n   */\n  async getEmployeeDataById(employeeId) {\n    try {\n      console.log(`Fetching employee data for ID: ${employeeId}`);\n      const response = await apiClient.get(`/employee-data/${employeeId}`);\n      console.log('Response received:', response.data);\n      return Array.isArray(response.data) ? response.data : [response.data];\n    } catch (error) {\n      console.error('Error in getEmployeeDataById:', error);\n      if (error.response) {\n        var _error$response$data2;\n        // Server responded with error status\n        const errorMessage = ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.message) || `Server error: ${error.response.status} - ${error.response.statusText}`;\n        throw new Error(errorMessage);\n      } else if (error.request) {\n        // Request was made but no response received\n        throw new Error('No response from server. Please check if the backend is running and accessible.');\n      } else {\n        // Something else happened\n        throw new Error('Error fetching employee by ID: ' + error.message);\n      }\n    }\n  },\n  /**\r\n   * Inserts new employee, department, and manager records.\r\n   * @param {Object} payload\r\n   * @returns {Promise<string>}\r\n   */\n  async insertEmployeeData(payload) {\n    try {\n      const response = await apiClient.post('/insert', payload);\n      return response.data;\n    } catch (error) {\n      if (error.response) {\n        var _error$response$data3;\n        throw new Error(((_error$response$data3 = error.response.data) === null || _error$response$data3 === void 0 ? void 0 : _error$response$data3.message) || `Insert failed: ${error.response.status}`);\n      } else if (error.request) {\n        throw new Error('Insert request failed. No server response.');\n      } else {\n        throw new Error('Insert error: ' + error.message);\n      }\n    }\n  }\n};\nexport default EmployeeService;", "map": {"version": 3, "names": ["axios", "BFF_BASE_URL", "apiClient", "create", "baseURL", "headers", "timeout", "EmployeeService", "getEmployeeData", "response", "get", "data", "error", "_error$response$data", "Error", "message", "status", "request", "getEmployeeDataById", "employeeId", "console", "log", "Array", "isArray", "_error$response$data2", "errorMessage", "statusText", "insertEmployeeData", "payload", "post", "_error$response$data3"], "sources": ["D:/FRONT-END-BACK-END-COMMUNICATION/frontend/src/services/EmployeeService.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Base URL for BFF API calls\r\nconst BFF_BASE_URL = 'http://localhost:4000/api/employees'; // Node.js BFF endpoint\r\n\r\n// Axios instance with BFF configuration\r\nconst apiClient = axios.create({\r\n  baseURL: BFF_BASE_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json'\r\n  },\r\n  timeout: 15000 // Increased timeout for BFF processing\r\n});\r\n\r\n// Service methods\r\nconst EmployeeService = {\r\n  /**\r\n   * Fetches all employee, department, and manager data.\r\n   * @returns {Promise<Object[]>}\r\n   */\r\n  async getEmployeeData() {\r\n    try {\r\n      const response = await apiClient.get('/employee-data-list');\r\n      return response.data;\r\n    } catch (error) {\r\n      if (error.response) {\r\n        throw new Error(error.response.data?.message || `Server error: ${error.response.status}`);\r\n      } else if (error.request) {\r\n        throw new Error('No response from server. Please check your connection.');\r\n      } else {\r\n        throw new Error('Error: ' + error.message);\r\n      }\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Fetches data for a specific employee ID.\r\n   * @param {number} employeeId\r\n   * @returns {Promise<Object[]>}\r\n   */\r\n  async getEmployeeDataById(employeeId) {\r\n    try {\r\n      console.log(`Fetching employee data for ID: ${employeeId}`);\r\n      const response = await apiClient.get(`/employee-data/${employeeId}`);\r\n      console.log('Response received:', response.data);\r\n      return Array.isArray(response.data) ? response.data : [response.data];\r\n    } catch (error) {\r\n      console.error('Error in getEmployeeDataById:', error);\r\n      if (error.response) {\r\n        // Server responded with error status\r\n        const errorMessage = error.response.data?.message ||\r\n          `Server error: ${error.response.status} - ${error.response.statusText}`;\r\n        throw new Error(errorMessage);\r\n      } else if (error.request) {\r\n        // Request was made but no response received\r\n        throw new Error('No response from server. Please check if the backend is running and accessible.');\r\n      } else {\r\n        // Something else happened\r\n        throw new Error('Error fetching employee by ID: ' + error.message);\r\n      }\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Inserts new employee, department, and manager records.\r\n   * @param {Object} payload\r\n   * @returns {Promise<string>}\r\n   */\r\n  async insertEmployeeData(payload) {\r\n    try {\r\n      const response = await apiClient.post('/insert', payload);\r\n      return response.data;\r\n    } catch (error) {\r\n      if (error.response) {\r\n        throw new Error(error.response.data?.message || `Insert failed: ${error.response.status}`);\r\n      } else if (error.request) {\r\n        throw new Error('Insert request failed. No server response.');\r\n      } else {\r\n        throw new Error('Insert error: ' + error.message);\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nexport default EmployeeService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,YAAY,GAAG,qCAAqC,CAAC,CAAC;;AAE5D;AACA,MAAMC,SAAS,GAAGF,KAAK,CAACG,MAAM,CAAC;EAC7BC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE,KAAK,CAAC;AACjB,CAAC,CAAC;;AAEF;AACA,MAAMC,eAAe,GAAG;EACtB;AACF;AACA;AACA;EACE,MAAMC,eAAeA,CAAA,EAAG;IACtB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMP,SAAS,CAACQ,GAAG,CAAC,qBAAqB,CAAC;MAC3D,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,IAAIA,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAAI,oBAAA;QAClB,MAAM,IAAIC,KAAK,CAAC,EAAAD,oBAAA,GAAAD,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAE,oBAAA,uBAAnBA,oBAAA,CAAqBE,OAAO,KAAI,iBAAiBH,KAAK,CAACH,QAAQ,CAACO,MAAM,EAAE,CAAC;MAC3F,CAAC,MAAM,IAAIJ,KAAK,CAACK,OAAO,EAAE;QACxB,MAAM,IAAIH,KAAK,CAAC,wDAAwD,CAAC;MAC3E,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAAC,SAAS,GAAGF,KAAK,CAACG,OAAO,CAAC;MAC5C;IACF;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAMG,mBAAmBA,CAACC,UAAU,EAAE;IACpC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,kCAAkCF,UAAU,EAAE,CAAC;MAC3D,MAAMV,QAAQ,GAAG,MAAMP,SAAS,CAACQ,GAAG,CAAC,kBAAkBS,UAAU,EAAE,CAAC;MACpEC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEZ,QAAQ,CAACE,IAAI,CAAC;MAChD,OAAOW,KAAK,CAACC,OAAO,CAACd,QAAQ,CAACE,IAAI,CAAC,GAAGF,QAAQ,CAACE,IAAI,GAAG,CAACF,QAAQ,CAACE,IAAI,CAAC;IACvE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,IAAIA,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAAe,qBAAA;QAClB;QACA,MAAMC,YAAY,GAAG,EAAAD,qBAAA,GAAAZ,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAa,qBAAA,uBAAnBA,qBAAA,CAAqBT,OAAO,KAC/C,iBAAiBH,KAAK,CAACH,QAAQ,CAACO,MAAM,MAAMJ,KAAK,CAACH,QAAQ,CAACiB,UAAU,EAAE;QACzE,MAAM,IAAIZ,KAAK,CAACW,YAAY,CAAC;MAC/B,CAAC,MAAM,IAAIb,KAAK,CAACK,OAAO,EAAE;QACxB;QACA,MAAM,IAAIH,KAAK,CAAC,iFAAiF,CAAC;MACpG,CAAC,MAAM;QACL;QACA,MAAM,IAAIA,KAAK,CAAC,iCAAiC,GAAGF,KAAK,CAACG,OAAO,CAAC;MACpE;IACF;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAMY,kBAAkBA,CAACC,OAAO,EAAE;IAChC,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMP,SAAS,CAAC2B,IAAI,CAAC,SAAS,EAAED,OAAO,CAAC;MACzD,OAAOnB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,IAAIA,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAAqB,qBAAA;QAClB,MAAM,IAAIhB,KAAK,CAAC,EAAAgB,qBAAA,GAAAlB,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAmB,qBAAA,uBAAnBA,qBAAA,CAAqBf,OAAO,KAAI,kBAAkBH,KAAK,CAACH,QAAQ,CAACO,MAAM,EAAE,CAAC;MAC5F,CAAC,MAAM,IAAIJ,KAAK,CAACK,OAAO,EAAE;QACxB,MAAM,IAAIH,KAAK,CAAC,4CAA4C,CAAC;MAC/D,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAAC,gBAAgB,GAAGF,KAAK,CAACG,OAAO,CAAC;MACnD;IACF;EACF;AACF,CAAC;AAED,eAAeR,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}