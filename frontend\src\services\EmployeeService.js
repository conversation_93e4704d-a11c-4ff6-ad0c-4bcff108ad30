import axios from 'axios';

// Base URL for BFF API calls
const BFF_BASE_URL = 'http://localhost:4000/api/employees'; // Node.js BFF endpoint

// Axios instance with BFF configuration
const apiClient = axios.create({
  baseURL: BFF_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 15000 // Increased timeout for BFF processing
});

// Service methods
const EmployeeService = {
  /**
   * Fetches all employee, department, and manager data via BFF.
   * @returns {Promise<Object[]>}
   */
  async getEmployeeData() {
    try {
      console.log('🔄 Fetching all employee data via BFF...');
      const response = await apiClient.get('/'); // BFF endpoint for all employees
      console.log('✅ BFF Response received:', response.data);
      return response.data.data || response.data; // Handle BFF response structure
    } catch (error) {
      console.error('❌ Error fetching employee data via BFF:', error);
      if (error.response) {
        const errorMessage = error.response.data?.message ||
          error.response.data?.error?.message ||
          `BFF error: ${error.response.status}`;
        throw new Error(errorMessage);
      } else if (error.request) {
        throw new Error('No response from BFF server. Please check your connection.');
      } else {
        throw new Error('Error: ' + error.message);
      }
    }
  },

  /**
   * Fetches data for a specific employee ID via BFF.
   * @param {number} employeeId
   * @returns {Promise<Object[]>}
   */
  async getEmployeeDataById(employeeId) {
    try {
      console.log(`🔄 Fetching employee data for ID: ${employeeId} via BFF...`);
      const response = await apiClient.get(`/${employeeId}`); // BFF endpoint for specific employee
      console.log('✅ BFF Response received:', response.data);

      const data = response.data.data || response.data;
      return Array.isArray(data) ? data : [data];
    } catch (error) {
      console.error('❌ Error in getEmployeeDataById via BFF:', error);
      if (error.response) {
        const errorMessage = error.response.data?.message ||
          error.response.data?.error?.message ||
          `BFF error: ${error.response.status} - ${error.response.statusText}`;
        throw new Error(errorMessage);
      } else if (error.request) {
        throw new Error('No response from BFF server. Please check if the BFF is running and accessible.');
      } else {
        throw new Error('Error fetching employee by ID: ' + error.message);
      }
    }
  },

  /**
   * Inserts new employee, department, and manager records via BFF.
   * @param {Object} payload
   * @returns {Promise<string>}
   */
  async insertEmployeeData(payload) {
    try {
      console.log('🔄 Inserting employee data via BFF...', payload);
      const response = await apiClient.post('/', payload); // BFF endpoint for insert
      console.log('✅ BFF Insert response:', response.data);
      return response.data.data || response.data.message || response.data;
    } catch (error) {
      console.error('❌ Error inserting employee data via BFF:', error);
      if (error.response) {
        const errorMessage = error.response.data?.message ||
          error.response.data?.error?.message ||
          `BFF insert failed: ${error.response.status}`;
        throw new Error(errorMessage);
      } else if (error.request) {
        throw new Error('Insert request failed. No BFF server response.');
      } else {
        throw new Error('Insert error: ' + error.message);
      }
    }
  }
};

export default EmployeeService;
