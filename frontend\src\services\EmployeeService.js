import axios from 'axios';

// Base URL for API calls
const API_BASE_URL = 'http://localhost:8080/employee-department-manager'; // Ensure your backend path is correct

// Axios instance with defaults
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 10000
});

// Service methods
const EmployeeService = {
  /**
   * Fetches all employee, department, and manager data.
   * @returns {Promise<Object[]>}
   */
  async getEmployeeData() {
    try {
      const response = await apiClient.get('/employee-data-list');
      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Error(error.response.data?.message || `Server error: ${error.response.status}`);
      } else if (error.request) {
        throw new Error('No response from server. Please check your connection.');
      } else {
        throw new Error('Error: ' + error.message);
      }
    }
  },

  /**
   * Fetches data for a specific employee ID.
   * @param {number} employeeId
   * @returns {Promise<Object[]>}
   */
  async getEmployeeDataById(employeeId) {
    try {
      const response = await apiClient.get(`/employee-data/${employeeId}`);
      return Array.isArray(response.data) ? response.data : [response.data];
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Error fetching employee by ID');
    }
  },

  /**
   * Inserts new employee, department, and manager records.
   * @param {Object} payload
   * @returns {Promise<string>}
   */
  async insertEmployeeData(payload) {
    try {
      const response = await apiClient.post('/insert', payload);
      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Error(error.response.data?.message || `Insert failed: ${error.response.status}`);
      } else if (error.request) {
        throw new Error('Insert request failed. No server response.');
      } else {
        throw new Error('Insert error: ' + error.message);
      }
    }
  }
};

export default EmployeeService;
