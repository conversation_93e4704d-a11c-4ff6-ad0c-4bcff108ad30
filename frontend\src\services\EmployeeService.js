import axios from 'axios';

// Base URL for API calls
const API_BASE_URL = 'http://localhost:8080/employee-department-manager'; // Ensure your backend path is correct

// Axios instance with defaults
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 10000
});

// Service methods
const EmployeeService = {
  /**
   * Fetches all employee, department, and manager data.
   * @returns {Promise<Object[]>}
   */
  async getEmployeeData() {
    try {
      const response = await apiClient.get('/employee-data-list');
      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Error(error.response.data?.message || `Server error: ${error.response.status}`);
      } else if (error.request) {
        throw new Error('No response from server. Please check your connection.');
      } else {
        throw new Error('Error: ' + error.message);
      }
    }
  },

  /**
   * Fetches data for a specific employee ID.
   * @param {number} employeeId
   * @returns {Promise<Object[]>}
   */
  async getEmployeeDataById(employeeId) {
    try {
      console.log(`Fetching employee data for ID: ${employeeId}`);
      const response = await apiClient.get(`/employee-data/${employeeId}`);
      console.log('Response received:', response.data);
      return Array.isArray(response.data) ? response.data : [response.data];
    } catch (error) {
      console.error('Error in getEmployeeDataById:', error);
      if (error.response) {
        // Server responded with error status
        const errorMessage = error.response.data?.message ||
          `Server error: ${error.response.status} - ${error.response.statusText}`;
        throw new Error(errorMessage);
      } else if (error.request) {
        // Request was made but no response received
        throw new Error('No response from server. Please check if the backend is running and accessible.');
      } else {
        // Something else happened
        throw new Error('Error fetching employee by ID: ' + error.message);
      }
    }
  },

  /**
   * Inserts new employee, department, and manager records.
   * @param {Object} payload
   * @returns {Promise<string>}
   */
  async insertEmployeeData(payload) {
    try {
      const response = await apiClient.post('/insert', payload);
      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Error(error.response.data?.message || `Insert failed: ${error.response.status}`);
      } else if (error.request) {
        throw new Error('Insert request failed. No server response.');
      } else {
        throw new Error('Insert error: ' + error.message);
      }
    }
  }
};

export default EmployeeService;
