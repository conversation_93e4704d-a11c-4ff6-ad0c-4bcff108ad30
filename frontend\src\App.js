import React, { useState, useEffect } from 'react';
import EmployeeService from './services/EmployeeService';
import EmployeeCard from './components/EmployeeCard';
import DepartmentCard from './components/DepartmentCard';
import ManagerCard from './components/ManagerCard';
import ErrorAlert from './components/ErrorAlert';
import LoadingSpinner from './components/LoadingSpinner';

function App() {
  const [data, setData] = useState([]); // list of combined records
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [formData, setFormData] = useState({
    deptName: '',
    deptCode: '',
    empName: '',
    employeeId: '',
    managerName: '',
    experience: ''
  });
  const [submitMessage, setSubmitMessage] = useState('');
  const [searchedEmployeeId, setSearchedEmployeeId] = useState(null); // Track if we're showing specific employee

  // Initial fetch - load all employee data on page load
  useEffect(() => {
    fetchData();
  }, []);

  // Fetch all data
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      setSearchedEmployeeId(null); // Clear search state
      const result = await EmployeeService.getEmployeeData();
      setData(result || []);
    } catch (err) {
      setError(err.message || 'Failed to fetch employee data');
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch by employee ID - shows only specific employee data
  const fetchByEmployeeId = async () => {
    if (!formData.employeeId) {
      setError('Please enter an Employee ID to search');
      return;
    }
    try {
      setLoading(true);
      setError(null);
      const employeeId = parseInt(formData.employeeId);
      const result = await EmployeeService.getEmployeeDataById(employeeId);
      setData(result || []);
      setSearchedEmployeeId(employeeId); // Set the searched employee ID
    } catch (err) {
      setError(err.message || 'Employee not found');
      setData([]);
      setSearchedEmployeeId(null);
    } finally {
      setLoading(false);
    }
  };

  // Reset to show all employee data
  const resetToAllData = async () => {
    // Clear the search input
    setFormData(prev => ({ ...prev, employeeId: '' }));
    // Clear search state and fetch all data
    setSearchedEmployeeId(null);
    await fetchData();
  };

  // Form input handler
  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  // Insert handler
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setSubmitMessage('');
      await EmployeeService.insertEmployeeData({
        deptName: formData.deptName,
        deptCode: formData.deptCode,
        empName: formData.empName,
        employeeId: parseInt(formData.employeeId),
        managerName: formData.managerName,
        experience: parseInt(formData.experience)
      });
      setSubmitMessage('Inserted successfully!');
      setFormData({
        deptName: '',
        deptCode: '',
        empName: '',
        employeeId: '',
        managerName: '',
        experience: ''
      });
      // After successful insert, show all employee data (including the new one)
      await fetchData();
    } catch (err) {
      setSubmitMessage('Failed to insert: ' + (err.response?.data?.message || err.message));
    }
  };

  return (
    <div className="container mt-5">

      {/* Search */}
      <div className="mb-4">
        <h4>Search Employee By ID</h4>
        <div className="input-group">
          <input
            type="number"
            className="form-control"
            placeholder="Enter Employee ID"
            value={formData.employeeId}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, employeeId: e.target.value }))
            }
          />
          <button className="btn btn-info" onClick={fetchByEmployeeId}>
            Search
          </button>
          <button className="btn btn-secondary ms-2" onClick={resetToAllData}>
            Reset
          </button>
        </div>
      </div>

      {/* Insert */}
      <h4>Add New Employee</h4>
      <form onSubmit={handleSubmit}>
        <div className="row mb-2">
          <div className="col">
            <input type="text" className="form-control" name="deptName" placeholder="Department Name" value={formData.deptName} onChange={handleInputChange} required />
          </div>
          <div className="col">
            <input type="text" className="form-control" name="deptCode" placeholder="Department Code" value={formData.deptCode} onChange={handleInputChange} required />
          </div>
        </div>

        <div className="row mb-2">
          <div className="col">
            <input type="text" className="form-control" name="empName" placeholder="Employee Name" value={formData.empName} onChange={handleInputChange} required />
          </div>
          <div className="col">
            <input type="number" className="form-control" name="employeeId" placeholder="Employee ID" value={formData.employeeId} onChange={handleInputChange} required />
          </div>
        </div>

        <div className="row mb-2">
          <div className="col">
            <input type="text" className="form-control" name="managerName" placeholder="Manager Name" value={formData.managerName} onChange={handleInputChange} required />
          </div>
          <div className="col">
            <input type="number" className="form-control" name="experience" placeholder="Manager Experience" value={formData.experience} onChange={handleInputChange} required />
          </div>
        </div>

        <button type="submit" className="btn btn-success mt-2">Submit</button>
        {submitMessage && <div className="mt-2">{submitMessage}</div>}
      </form>

      {/* Display Section */}
      <div className="d-flex justify-content-between align-items-center mt-4 mb-3">
        <div>
          <h1>Employee Dashboard</h1>
          {searchedEmployeeId && (
            <small className="text-muted">
              Showing data for Employee ID: {searchedEmployeeId}
            </small>
          )}
          {!searchedEmployeeId && data.length > 0 && (
            <small className="text-muted">
              Showing all employees ({data.length} records)
            </small>
          )}
        </div>
        <button
          className="btn btn-primary"
          onClick={resetToAllData}
          disabled={loading}
        >
          {loading ? 'Loading...' : 'Show All Employees'}
        </button>
      </div>

      {loading ? (
        <LoadingSpinner />
      ) : error ? (
        <ErrorAlert message={error} retryFn={fetchData} />
      ) : (
        <>
          {Array.isArray(data) && data.length > 0 ? (
            data.map((row, idx) => (
              <div className="row mb-4" key={idx}>
                <div className="col-md-4">
                  <EmployeeCard employee={row.employee} />
                </div>
                <div className="col-md-4">
                  <DepartmentCard department={row.department} />
                </div>
                <div className="col-md-4">
                  <ManagerCard manager={row.manager} />
                </div>
              </div>
            ))
          ) : (
            <div>No records found.</div>
          )}
        </>
      )}
    </div>
  );
}

export default App;
