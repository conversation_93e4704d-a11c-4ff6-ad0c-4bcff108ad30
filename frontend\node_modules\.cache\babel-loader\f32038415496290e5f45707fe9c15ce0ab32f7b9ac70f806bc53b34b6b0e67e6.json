{"ast": null, "code": "import axios from 'axios';\n\n// Base URL for BFF API calls\nconst BFF_BASE_URL = 'http://localhost:4000/api/employees'; // Node.js BFF endpoint\n\n// Axios instance with BFF configuration\nconst apiClient = axios.create({\n  baseURL: BFF_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 15000 // Increased timeout for BFF processing\n});\n\n// Service methods\nconst EmployeeService = {\n  /**\r\n   * Fetches all employee, department, and manager data via BFF.\r\n   * @returns {Promise<Object[]>}\r\n   */\n  async getEmployeeData() {\n    try {\n      console.log('🔄 Fetching all employee data via BFF...');\n      const response = await apiClient.get('/'); // BFF endpoint for all employees\n      console.log('✅ BFF Response received:', response.data);\n      return response.data.data || response.data; // Handle BFF response structure\n    } catch (error) {\n      console.error('❌ Error fetching employee data via BFF:', error);\n      if (error.response) {\n        var _error$response$data, _error$response$data2, _error$response$data3;\n        const errorMessage = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : (_error$response$data3 = _error$response$data2.error) === null || _error$response$data3 === void 0 ? void 0 : _error$response$data3.message) || `BFF error: ${error.response.status}`;\n        throw new Error(errorMessage);\n      } else if (error.request) {\n        throw new Error('No response from BFF server. Please check your connection.');\n      } else {\n        throw new Error('Error: ' + error.message);\n      }\n    }\n  },\n  /**\r\n   * Fetches data for a specific employee ID.\r\n   * @param {number} employeeId\r\n   * @returns {Promise<Object[]>}\r\n   */\n  async getEmployeeDataById(employeeId) {\n    try {\n      console.log(`Fetching employee data for ID: ${employeeId}`);\n      const response = await apiClient.get(`/employee-data/${employeeId}`);\n      console.log('Response received:', response.data);\n      return Array.isArray(response.data) ? response.data : [response.data];\n    } catch (error) {\n      console.error('Error in getEmployeeDataById:', error);\n      if (error.response) {\n        var _error$response$data4;\n        // Server responded with error status\n        const errorMessage = ((_error$response$data4 = error.response.data) === null || _error$response$data4 === void 0 ? void 0 : _error$response$data4.message) || `Server error: ${error.response.status} - ${error.response.statusText}`;\n        throw new Error(errorMessage);\n      } else if (error.request) {\n        // Request was made but no response received\n        throw new Error('No response from server. Please check if the backend is running and accessible.');\n      } else {\n        // Something else happened\n        throw new Error('Error fetching employee by ID: ' + error.message);\n      }\n    }\n  },\n  /**\r\n   * Inserts new employee, department, and manager records.\r\n   * @param {Object} payload\r\n   * @returns {Promise<string>}\r\n   */\n  async insertEmployeeData(payload) {\n    try {\n      const response = await apiClient.post('/insert', payload);\n      return response.data;\n    } catch (error) {\n      if (error.response) {\n        var _error$response$data5;\n        throw new Error(((_error$response$data5 = error.response.data) === null || _error$response$data5 === void 0 ? void 0 : _error$response$data5.message) || `Insert failed: ${error.response.status}`);\n      } else if (error.request) {\n        throw new Error('Insert request failed. No server response.');\n      } else {\n        throw new Error('Insert error: ' + error.message);\n      }\n    }\n  }\n};\nexport default EmployeeService;", "map": {"version": 3, "names": ["axios", "BFF_BASE_URL", "apiClient", "create", "baseURL", "headers", "timeout", "EmployeeService", "getEmployeeData", "console", "log", "response", "get", "data", "error", "_error$response$data", "_error$response$data2", "_error$response$data3", "errorMessage", "message", "status", "Error", "request", "getEmployeeDataById", "employeeId", "Array", "isArray", "_error$response$data4", "statusText", "insertEmployeeData", "payload", "post", "_error$response$data5"], "sources": ["D:/FRONT-END-BACK-END-COMMUNICATION/frontend/src/services/EmployeeService.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Base URL for BFF API calls\r\nconst BFF_BASE_URL = 'http://localhost:4000/api/employees'; // Node.js BFF endpoint\r\n\r\n// Axios instance with BFF configuration\r\nconst apiClient = axios.create({\r\n  baseURL: BFF_BASE_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json'\r\n  },\r\n  timeout: 15000 // Increased timeout for BFF processing\r\n});\r\n\r\n// Service methods\r\nconst EmployeeService = {\r\n  /**\r\n   * Fetches all employee, department, and manager data via BFF.\r\n   * @returns {Promise<Object[]>}\r\n   */\r\n  async getEmployeeData() {\r\n    try {\r\n      console.log('🔄 Fetching all employee data via BFF...');\r\n      const response = await apiClient.get('/'); // BFF endpoint for all employees\r\n      console.log('✅ BFF Response received:', response.data);\r\n      return response.data.data || response.data; // Handle BFF response structure\r\n    } catch (error) {\r\n      console.error('❌ Error fetching employee data via BFF:', error);\r\n      if (error.response) {\r\n        const errorMessage = error.response.data?.message ||\r\n          error.response.data?.error?.message ||\r\n          `BFF error: ${error.response.status}`;\r\n        throw new Error(errorMessage);\r\n      } else if (error.request) {\r\n        throw new Error('No response from BFF server. Please check your connection.');\r\n      } else {\r\n        throw new Error('Error: ' + error.message);\r\n      }\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Fetches data for a specific employee ID.\r\n   * @param {number} employeeId\r\n   * @returns {Promise<Object[]>}\r\n   */\r\n  async getEmployeeDataById(employeeId) {\r\n    try {\r\n      console.log(`Fetching employee data for ID: ${employeeId}`);\r\n      const response = await apiClient.get(`/employee-data/${employeeId}`);\r\n      console.log('Response received:', response.data);\r\n      return Array.isArray(response.data) ? response.data : [response.data];\r\n    } catch (error) {\r\n      console.error('Error in getEmployeeDataById:', error);\r\n      if (error.response) {\r\n        // Server responded with error status\r\n        const errorMessage = error.response.data?.message ||\r\n          `Server error: ${error.response.status} - ${error.response.statusText}`;\r\n        throw new Error(errorMessage);\r\n      } else if (error.request) {\r\n        // Request was made but no response received\r\n        throw new Error('No response from server. Please check if the backend is running and accessible.');\r\n      } else {\r\n        // Something else happened\r\n        throw new Error('Error fetching employee by ID: ' + error.message);\r\n      }\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Inserts new employee, department, and manager records.\r\n   * @param {Object} payload\r\n   * @returns {Promise<string>}\r\n   */\r\n  async insertEmployeeData(payload) {\r\n    try {\r\n      const response = await apiClient.post('/insert', payload);\r\n      return response.data;\r\n    } catch (error) {\r\n      if (error.response) {\r\n        throw new Error(error.response.data?.message || `Insert failed: ${error.response.status}`);\r\n      } else if (error.request) {\r\n        throw new Error('Insert request failed. No server response.');\r\n      } else {\r\n        throw new Error('Insert error: ' + error.message);\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nexport default EmployeeService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,YAAY,GAAG,qCAAqC,CAAC,CAAC;;AAE5D;AACA,MAAMC,SAAS,GAAGF,KAAK,CAACG,MAAM,CAAC;EAC7BC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE,KAAK,CAAC;AACjB,CAAC,CAAC;;AAEF;AACA,MAAMC,eAAe,GAAG;EACtB;AACF;AACA;AACA;EACE,MAAMC,eAAeA,CAAA,EAAG;IACtB,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,MAAMC,QAAQ,GAAG,MAAMT,SAAS,CAACU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3CH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,QAAQ,CAACE,IAAI,CAAC;MACtD,OAAOF,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAIA,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAAI,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QAClB,MAAMC,YAAY,GAAG,EAAAH,oBAAA,GAAAD,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAE,oBAAA,uBAAnBA,oBAAA,CAAqBI,OAAO,OAAAH,qBAAA,GAC/CF,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAG,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBF,KAAK,cAAAG,qBAAA,uBAA1BA,qBAAA,CAA4BE,OAAO,KACnC,cAAcL,KAAK,CAACH,QAAQ,CAACS,MAAM,EAAE;QACvC,MAAM,IAAIC,KAAK,CAACH,YAAY,CAAC;MAC/B,CAAC,MAAM,IAAIJ,KAAK,CAACQ,OAAO,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,4DAA4D,CAAC;MAC/E,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAAC,SAAS,GAAGP,KAAK,CAACK,OAAO,CAAC;MAC5C;IACF;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAMI,mBAAmBA,CAACC,UAAU,EAAE;IACpC,IAAI;MACFf,OAAO,CAACC,GAAG,CAAC,kCAAkCc,UAAU,EAAE,CAAC;MAC3D,MAAMb,QAAQ,GAAG,MAAMT,SAAS,CAACU,GAAG,CAAC,kBAAkBY,UAAU,EAAE,CAAC;MACpEf,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAChD,OAAOY,KAAK,CAACC,OAAO,CAACf,QAAQ,CAACE,IAAI,CAAC,GAAGF,QAAQ,CAACE,IAAI,GAAG,CAACF,QAAQ,CAACE,IAAI,CAAC;IACvE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,IAAIA,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAAgB,qBAAA;QAClB;QACA,MAAMT,YAAY,GAAG,EAAAS,qBAAA,GAAAb,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAc,qBAAA,uBAAnBA,qBAAA,CAAqBR,OAAO,KAC/C,iBAAiBL,KAAK,CAACH,QAAQ,CAACS,MAAM,MAAMN,KAAK,CAACH,QAAQ,CAACiB,UAAU,EAAE;QACzE,MAAM,IAAIP,KAAK,CAACH,YAAY,CAAC;MAC/B,CAAC,MAAM,IAAIJ,KAAK,CAACQ,OAAO,EAAE;QACxB;QACA,MAAM,IAAID,KAAK,CAAC,iFAAiF,CAAC;MACpG,CAAC,MAAM;QACL;QACA,MAAM,IAAIA,KAAK,CAAC,iCAAiC,GAAGP,KAAK,CAACK,OAAO,CAAC;MACpE;IACF;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAMU,kBAAkBA,CAACC,OAAO,EAAE;IAChC,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMT,SAAS,CAAC6B,IAAI,CAAC,SAAS,EAAED,OAAO,CAAC;MACzD,OAAOnB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,IAAIA,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAAqB,qBAAA;QAClB,MAAM,IAAIX,KAAK,CAAC,EAAAW,qBAAA,GAAAlB,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAmB,qBAAA,uBAAnBA,qBAAA,CAAqBb,OAAO,KAAI,kBAAkBL,KAAK,CAACH,QAAQ,CAACS,MAAM,EAAE,CAAC;MAC5F,CAAC,MAAM,IAAIN,KAAK,CAACQ,OAAO,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,4CAA4C,CAAC;MAC/D,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAAC,gBAAgB,GAAGP,KAAK,CAACK,OAAO,CAAC;MACnD;IACF;EACF;AACF,CAAC;AAED,eAAeZ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}