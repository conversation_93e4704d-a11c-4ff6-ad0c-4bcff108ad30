[{"D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\index.js": "1", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\EmployeeCard.js": "2", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\ErrorAlert.js": "3", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\LoadingSpinner.js": "4", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\DepartmentCard.js": "5", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\ManagerCard.js": "6", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\services\\EmployeeService.js": "7", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\App.js": "8", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\services\\AuthService.js": "9", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\ProtectedRoute.js": "10", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\Header.js": "11", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\Login.js": "12", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\EmployeeDashboard.js": "13"}, {"size": 416, "mtime": 1751532374048, "results": "14", "hashOfConfig": "15"}, {"size": 881, "mtime": 1750958223507, "results": "16", "hashOfConfig": "15"}, {"size": 493, "mtime": 1750006709206, "results": "17", "hashOfConfig": "15"}, {"size": 355, "mtime": 1750006709206, "results": "18", "hashOfConfig": "15"}, {"size": 860, "mtime": 1750958238823, "results": "19", "hashOfConfig": "15"}, {"size": 848, "mtime": 1750958253717, "results": "20", "hashOfConfig": "15"}, {"size": 4548, "mtime": 1751543332261, "results": "21", "hashOfConfig": "15"}, {"size": 1513, "mtime": 1751531424291, "results": "22", "hashOfConfig": "15"}, {"size": 9181, "mtime": 1751531126665, "results": "23", "hashOfConfig": "15"}, {"size": 4894, "mtime": 1751531191543, "results": "24", "hashOfConfig": "15"}, {"size": 5422, "mtime": 1751531212160, "results": "25", "hashOfConfig": "15"}, {"size": 7290, "mtime": 1751531169998, "results": "26", "hashOfConfig": "15"}, {"size": 13885, "mtime": 1751543350865, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10k00xz", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\index.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\EmployeeCard.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\ErrorAlert.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\LoadingSpinner.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\DepartmentCard.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\ManagerCard.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\services\\EmployeeService.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\App.js", ["67"], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\services\\AuthService.js", ["68"], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\Header.js", ["69"], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\Login.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\EmployeeDashboard.js", [], [], {"ruleId": "70", "severity": 1, "message": "71", "line": 10, "column": 10, "nodeType": "72", "messageId": "73", "endLine": 10, "endColumn": 14}, {"ruleId": "74", "severity": 1, "message": "75", "line": 356, "column": 1, "nodeType": "76", "endLine": 356, "endColumn": 34}, {"ruleId": "77", "severity": 1, "message": "78", "line": 98, "column": 15, "nodeType": "79", "endLine": 105, "endColumn": 16}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]