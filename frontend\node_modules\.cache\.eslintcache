[{"D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\index.js": "1", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\EmployeeCard.js": "2", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\ErrorAlert.js": "3", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\LoadingSpinner.js": "4", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\DepartmentCard.js": "5", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\ManagerCard.js": "6", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\services\\EmployeeService.js": "7", "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\App.js": "8"}, {"size": 311, "mtime": 1750006709206, "results": "9", "hashOfConfig": "10"}, {"size": 881, "mtime": 1750958223507, "results": "11", "hashOfConfig": "10"}, {"size": 493, "mtime": 1750006709206, "results": "12", "hashOfConfig": "10"}, {"size": 355, "mtime": 1750006709206, "results": "13", "hashOfConfig": "10"}, {"size": 860, "mtime": 1750958238823, "results": "14", "hashOfConfig": "10"}, {"size": 848, "mtime": 1750958253717, "results": "15", "hashOfConfig": "10"}, {"size": 3669, "mtime": 1751522395371, "results": "16", "hashOfConfig": "10"}, {"size": 7722, "mtime": 1751518609403, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10k00xz", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\index.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\EmployeeCard.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\ErrorAlert.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\LoadingSpinner.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\DepartmentCard.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\components\\ManagerCard.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\services\\EmployeeService.js", [], [], "D:\\FRONT-END-BACK-END-COMMUNICATION\\frontend\\src\\App.js", [], []]