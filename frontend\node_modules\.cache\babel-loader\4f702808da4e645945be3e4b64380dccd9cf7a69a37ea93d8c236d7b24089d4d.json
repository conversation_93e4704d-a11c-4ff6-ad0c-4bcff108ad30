{"ast": null, "code": "var _jsxFileName = \"D:\\\\FRONT-END-BACK-END-COMMUNICATION\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport AuthService from './services/AuthService';\nimport Login from './components/Login';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Header from './components/Header';\nimport EmployeeDashboard from './components/EmployeeDashboard';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [user, setUser] = useState(null);\n  useEffect(() => {\n    // Check if user is already authenticated\n    const userData = AuthService.getCurrentUserData();\n    setUser(userData);\n  }, []);\n  const handleLogin = userData => {\n    setUser(userData);\n  };\n\n  // Fetch all data\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      setSearchedEmployeeId(null); // Clear search state\n      const result = await EmployeeService.getEmployeeData();\n      setData(result || []);\n    } catch (err) {\n      setError(err.message || 'Failed to fetch employee data');\n      setData([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch by employee ID - shows only specific employee data\n  const fetchByEmployeeId = async () => {\n    if (!formData.employeeId) {\n      setError('Please enter an Employee ID to search');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n      const employeeId = parseInt(formData.employeeId);\n      const result = await EmployeeService.getEmployeeDataById(employeeId);\n      setData(result || []);\n      setSearchedEmployeeId(employeeId); // Set the searched employee ID\n    } catch (err) {\n      setError(err.message || 'Employee not found');\n      setData([]);\n      setSearchedEmployeeId(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Reset to show all employee data\n  const resetToAllData = async () => {\n    // Clear the search input\n    setFormData(prev => ({\n      ...prev,\n      employeeId: ''\n    }));\n    // Clear search state and fetch all data\n    setSearchedEmployeeId(null);\n    await fetchData();\n  };\n\n  // Form input handler\n  const handleInputChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Insert handler\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      setSubmitMessage('');\n      await EmployeeService.insertEmployeeData({\n        deptName: formData.deptName,\n        deptCode: formData.deptCode,\n        empName: formData.empName,\n        employeeId: parseInt(formData.employeeId),\n        managerName: formData.managerName,\n        experience: parseInt(formData.experience)\n      });\n      setSubmitMessage('Inserted successfully!');\n      setFormData({\n        deptName: '',\n        deptCode: '',\n        empName: '',\n        employeeId: '',\n        managerName: '',\n        experience: ''\n      });\n      // After successful insert, show all employee data (including the new one)\n      await fetchData();\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setSubmitMessage('Failed to insert: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mt-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Search Employee By ID\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          className: \"form-control\",\n          placeholder: \"Enter Employee ID\",\n          value: formData.employeeId,\n          onChange: e => setFormData(prev => ({\n            ...prev,\n            employeeId: e.target.value\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-info\",\n          onClick: fetchByEmployeeId,\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary ms-2\",\n          onClick: resetToAllData,\n          children: \"Reset\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n      children: \"Add New Employee\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            name: \"deptName\",\n            placeholder: \"Department Name\",\n            value: formData.deptName,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            name: \"deptCode\",\n            placeholder: \"Department Code\",\n            value: formData.deptCode,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            name: \"empName\",\n            placeholder: \"Employee Name\",\n            value: formData.empName,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            className: \"form-control\",\n            name: \"employeeId\",\n            placeholder: \"Employee ID\",\n            value: formData.employeeId,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            name: \"managerName\",\n            placeholder: \"Manager Name\",\n            value: formData.managerName,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            className: \"form-control\",\n            name: \"experience\",\n            placeholder: \"Manager Experience\",\n            value: formData.experience,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"btn btn-success mt-2\",\n        children: \"Submit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), submitMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: submitMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 27\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mt-4 mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Employee Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), searchedEmployeeId && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-muted\",\n          children: [\"Showing data for Employee ID: \", searchedEmployeeId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this), !searchedEmployeeId && data.length > 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-muted\",\n          children: [\"Showing all employees (\", data.length, \" records)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: resetToAllData,\n        disabled: loading,\n        children: loading ? 'Loading...' : 'Show All Employees'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(ErrorAlert, {\n      message: error,\n      retryFn: fetchData\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: Array.isArray(data) && data.length > 0 ? data.map((row, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-4\",\n          children: /*#__PURE__*/_jsxDEV(EmployeeCard, {\n            employee: row.employee\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-4\",\n          children: /*#__PURE__*/_jsxDEV(DepartmentCard, {\n            department: row.department\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-4\",\n          children: /*#__PURE__*/_jsxDEV(ManagerCard, {\n            manager: row.manager\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 17\n        }, this)]\n      }, idx, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 15\n      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"No records found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 13\n      }, this)\n    }, void 0, false)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"5s2qRsV95gTJBmaaTh11GoxYeGE=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "AuthService", "<PERSON><PERSON>", "ProtectedRoute", "Header", "EmployeeDashboard", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "user", "setUser", "userData", "getCurrentUserData", "handleLogin", "fetchData", "setLoading", "setError", "setSearchedEmployeeId", "result", "EmployeeService", "getEmployeeData", "setData", "err", "message", "fetchByEmployeeId", "formData", "employeeId", "parseInt", "getEmployeeDataById", "resetToAllData", "setFormData", "prev", "handleInputChange", "event", "name", "value", "target", "handleSubmit", "e", "preventDefault", "setSubmitMessage", "insertEmployeeData", "deptName", "deptCode", "empName", "<PERSON><PERSON><PERSON>", "experience", "_err$response", "_err$response$data", "response", "data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "onClick", "onSubmit", "required", "submitMessage", "searchedEmployeeId", "length", "disabled", "loading", "LoadingSpinner", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retryFn", "Array", "isArray", "map", "row", "idx", "EmployeeCard", "employee", "DepartmentCard", "department", "ManagerCard", "manager", "_c", "$RefreshReg$"], "sources": ["D:/FRONT-END-BACK-END-COMMUNICATION/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\r\nimport AuthService from './services/AuthService';\r\nimport Login from './components/Login';\r\nimport ProtectedRoute from './components/ProtectedRoute';\r\nimport Header from './components/Header';\r\nimport EmployeeDashboard from './components/EmployeeDashboard';\r\n\r\nfunction App() {\r\n  const [user, setUser] = useState(null);\r\n\r\n  useEffect(() => {\r\n    // Check if user is already authenticated\r\n    const userData = AuthService.getCurrentUserData();\r\n    setUser(userData);\r\n  }, []);\r\n\r\n  const handleLogin = (userData) => {\r\n    setUser(userData);\r\n  };\r\n\r\n  // Fetch all data\r\n  const fetchData = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      setSearchedEmployeeId(null); // Clear search state\r\n      const result = await EmployeeService.getEmployeeData();\r\n      setData(result || []);\r\n    } catch (err) {\r\n      setError(err.message || 'Failed to fetch employee data');\r\n      setData([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Fetch by employee ID - shows only specific employee data\r\n  const fetchByEmployeeId = async () => {\r\n    if (!formData.employeeId) {\r\n      setError('Please enter an Employee ID to search');\r\n      return;\r\n    }\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      const employeeId = parseInt(formData.employeeId);\r\n      const result = await EmployeeService.getEmployeeDataById(employeeId);\r\n      setData(result || []);\r\n      setSearchedEmployeeId(employeeId); // Set the searched employee ID\r\n    } catch (err) {\r\n      setError(err.message || 'Employee not found');\r\n      setData([]);\r\n      setSearchedEmployeeId(null);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Reset to show all employee data\r\n  const resetToAllData = async () => {\r\n    // Clear the search input\r\n    setFormData(prev => ({ ...prev, employeeId: '' }));\r\n    // Clear search state and fetch all data\r\n    setSearchedEmployeeId(null);\r\n    await fetchData();\r\n  };\r\n\r\n  // Form input handler\r\n  const handleInputChange = (event) => {\r\n    const { name, value } = event.target;\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  // Insert handler\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      setSubmitMessage('');\r\n      await EmployeeService.insertEmployeeData({\r\n        deptName: formData.deptName,\r\n        deptCode: formData.deptCode,\r\n        empName: formData.empName,\r\n        employeeId: parseInt(formData.employeeId),\r\n        managerName: formData.managerName,\r\n        experience: parseInt(formData.experience)\r\n      });\r\n      setSubmitMessage('Inserted successfully!');\r\n      setFormData({\r\n        deptName: '',\r\n        deptCode: '',\r\n        empName: '',\r\n        employeeId: '',\r\n        managerName: '',\r\n        experience: ''\r\n      });\r\n      // After successful insert, show all employee data (including the new one)\r\n      await fetchData();\r\n    } catch (err) {\r\n      setSubmitMessage('Failed to insert: ' + (err.response?.data?.message || err.message));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container mt-5\">\r\n\r\n      {/* Search */}\r\n      <div className=\"mb-4\">\r\n        <h4>Search Employee By ID</h4>\r\n        <div className=\"input-group\">\r\n          <input\r\n            type=\"number\"\r\n            className=\"form-control\"\r\n            placeholder=\"Enter Employee ID\"\r\n            value={formData.employeeId}\r\n            onChange={(e) =>\r\n              setFormData((prev) => ({ ...prev, employeeId: e.target.value }))\r\n            }\r\n          />\r\n          <button className=\"btn btn-info\" onClick={fetchByEmployeeId}>\r\n            Search\r\n          </button>\r\n          <button className=\"btn btn-secondary ms-2\" onClick={resetToAllData}>\r\n            Reset\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Insert */}\r\n      <h4>Add New Employee</h4>\r\n      <form onSubmit={handleSubmit}>\r\n        <div className=\"row mb-2\">\r\n          <div className=\"col\">\r\n            <input type=\"text\" className=\"form-control\" name=\"deptName\" placeholder=\"Department Name\" value={formData.deptName} onChange={handleInputChange} required />\r\n          </div>\r\n          <div className=\"col\">\r\n            <input type=\"text\" className=\"form-control\" name=\"deptCode\" placeholder=\"Department Code\" value={formData.deptCode} onChange={handleInputChange} required />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"row mb-2\">\r\n          <div className=\"col\">\r\n            <input type=\"text\" className=\"form-control\" name=\"empName\" placeholder=\"Employee Name\" value={formData.empName} onChange={handleInputChange} required />\r\n          </div>\r\n          <div className=\"col\">\r\n            <input type=\"number\" className=\"form-control\" name=\"employeeId\" placeholder=\"Employee ID\" value={formData.employeeId} onChange={handleInputChange} required />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"row mb-2\">\r\n          <div className=\"col\">\r\n            <input type=\"text\" className=\"form-control\" name=\"managerName\" placeholder=\"Manager Name\" value={formData.managerName} onChange={handleInputChange} required />\r\n          </div>\r\n          <div className=\"col\">\r\n            <input type=\"number\" className=\"form-control\" name=\"experience\" placeholder=\"Manager Experience\" value={formData.experience} onChange={handleInputChange} required />\r\n          </div>\r\n        </div>\r\n\r\n        <button type=\"submit\" className=\"btn btn-success mt-2\">Submit</button>\r\n        {submitMessage && <div className=\"mt-2\">{submitMessage}</div>}\r\n      </form>\r\n\r\n      {/* Display Section */}\r\n      <div className=\"d-flex justify-content-between align-items-center mt-4 mb-3\">\r\n        <div>\r\n          <h1>Employee Dashboard</h1>\r\n          {searchedEmployeeId && (\r\n            <small className=\"text-muted\">\r\n              Showing data for Employee ID: {searchedEmployeeId}\r\n            </small>\r\n          )}\r\n          {!searchedEmployeeId && data.length > 0 && (\r\n            <small className=\"text-muted\">\r\n              Showing all employees ({data.length} records)\r\n            </small>\r\n          )}\r\n        </div>\r\n        <button\r\n          className=\"btn btn-primary\"\r\n          onClick={resetToAllData}\r\n          disabled={loading}\r\n        >\r\n          {loading ? 'Loading...' : 'Show All Employees'}\r\n        </button>\r\n      </div>\r\n\r\n      {loading ? (\r\n        <LoadingSpinner />\r\n      ) : error ? (\r\n        <ErrorAlert message={error} retryFn={fetchData} />\r\n      ) : (\r\n        <>\r\n          {Array.isArray(data) && data.length > 0 ? (\r\n            data.map((row, idx) => (\r\n              <div className=\"row mb-4\" key={idx}>\r\n                <div className=\"col-md-4\">\r\n                  <EmployeeCard employee={row.employee} />\r\n                </div>\r\n                <div className=\"col-md-4\">\r\n                  <DepartmentCard department={row.department} />\r\n                </div>\r\n                <div className=\"col-md-4\">\r\n                  <ManagerCard manager={row.manager} />\r\n                </div>\r\n              </div>\r\n            ))\r\n          ) : (\r\n            <div>No records found.</div>\r\n          )}\r\n        </>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,iBAAiB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/D,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd;IACA,MAAMmB,QAAQ,GAAGb,WAAW,CAACc,kBAAkB,CAAC,CAAC;IACjDF,OAAO,CAACC,QAAQ,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,WAAW,GAAIF,QAAQ,IAAK;IAChCD,OAAO,CAACC,QAAQ,CAAC;EACnB,CAAC;;EAED;EACA,MAAMG,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFC,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC;MACdC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;MAC7B,MAAMC,MAAM,GAAG,MAAMC,eAAe,CAACC,eAAe,CAAC,CAAC;MACtDC,OAAO,CAACH,MAAM,IAAI,EAAE,CAAC;IACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZN,QAAQ,CAACM,GAAG,CAACC,OAAO,IAAI,+BAA+B,CAAC;MACxDF,OAAO,CAAC,EAAE,CAAC;IACb,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACC,QAAQ,CAACC,UAAU,EAAE;MACxBV,QAAQ,CAAC,uCAAuC,CAAC;MACjD;IACF;IACA,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMU,UAAU,GAAGC,QAAQ,CAACF,QAAQ,CAACC,UAAU,CAAC;MAChD,MAAMR,MAAM,GAAG,MAAMC,eAAe,CAACS,mBAAmB,CAACF,UAAU,CAAC;MACpEL,OAAO,CAACH,MAAM,IAAI,EAAE,CAAC;MACrBD,qBAAqB,CAACS,UAAU,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZN,QAAQ,CAACM,GAAG,CAACC,OAAO,IAAI,oBAAoB,CAAC;MAC7CF,OAAO,CAAC,EAAE,CAAC;MACXJ,qBAAqB,CAAC,IAAI,CAAC;IAC7B,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMc,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC;IACAC,WAAW,CAACC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEL,UAAU,EAAE;IAAG,CAAC,CAAC,CAAC;IAClD;IACAT,qBAAqB,CAAC,IAAI,CAAC;IAC3B,MAAMH,SAAS,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMkB,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,KAAK,CAACG,MAAM;IACpCN,WAAW,CAAEC,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAACG,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACFC,gBAAgB,CAAC,EAAE,CAAC;MACpB,MAAMrB,eAAe,CAACsB,kBAAkB,CAAC;QACvCC,QAAQ,EAAEjB,QAAQ,CAACiB,QAAQ;QAC3BC,QAAQ,EAAElB,QAAQ,CAACkB,QAAQ;QAC3BC,OAAO,EAAEnB,QAAQ,CAACmB,OAAO;QACzBlB,UAAU,EAAEC,QAAQ,CAACF,QAAQ,CAACC,UAAU,CAAC;QACzCmB,WAAW,EAAEpB,QAAQ,CAACoB,WAAW;QACjCC,UAAU,EAAEnB,QAAQ,CAACF,QAAQ,CAACqB,UAAU;MAC1C,CAAC,CAAC;MACFN,gBAAgB,CAAC,wBAAwB,CAAC;MAC1CV,WAAW,CAAC;QACVY,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,OAAO,EAAE,EAAE;QACXlB,UAAU,EAAE,EAAE;QACdmB,WAAW,EAAE,EAAE;QACfC,UAAU,EAAE;MACd,CAAC,CAAC;MACF;MACA,MAAMhC,SAAS,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOQ,GAAG,EAAE;MAAA,IAAAyB,aAAA,EAAAC,kBAAA;MACZR,gBAAgB,CAAC,oBAAoB,IAAI,EAAAO,aAAA,GAAAzB,GAAG,CAAC2B,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcG,IAAI,cAAAF,kBAAA,uBAAlBA,kBAAA,CAAoBzB,OAAO,KAAID,GAAG,CAACC,OAAO,CAAC,CAAC;IACvF;EACF,CAAC;EAED,oBACEnB,OAAA;IAAK+C,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAG7BhD,OAAA;MAAK+C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBhD,OAAA;QAAAgD,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BpD,OAAA;QAAK+C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BhD,OAAA;UACEqD,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,cAAc;UACxBO,WAAW,EAAC,mBAAmB;UAC/BvB,KAAK,EAAEV,QAAQ,CAACC,UAAW;UAC3BiC,QAAQ,EAAGrB,CAAC,IACVR,WAAW,CAAEC,IAAI,KAAM;YAAE,GAAGA,IAAI;YAAEL,UAAU,EAAEY,CAAC,CAACF,MAAM,CAACD;UAAM,CAAC,CAAC;QAChE;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFpD,OAAA;UAAQ+C,SAAS,EAAC,cAAc;UAACS,OAAO,EAAEpC,iBAAkB;UAAA4B,QAAA,EAAC;QAE7D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpD,OAAA;UAAQ+C,SAAS,EAAC,wBAAwB;UAACS,OAAO,EAAE/B,cAAe;UAAAuB,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpD,OAAA;MAAAgD,QAAA,EAAI;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzBpD,OAAA;MAAMyD,QAAQ,EAAExB,YAAa;MAAAe,QAAA,gBAC3BhD,OAAA;QAAK+C,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBhD,OAAA;UAAK+C,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBhD,OAAA;YAAOqD,IAAI,EAAC,MAAM;YAACN,SAAS,EAAC,cAAc;YAACjB,IAAI,EAAC,UAAU;YAACwB,WAAW,EAAC,iBAAiB;YAACvB,KAAK,EAAEV,QAAQ,CAACiB,QAAS;YAACiB,QAAQ,EAAE3B,iBAAkB;YAAC8B,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzJ,CAAC,eACNpD,OAAA;UAAK+C,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBhD,OAAA;YAAOqD,IAAI,EAAC,MAAM;YAACN,SAAS,EAAC,cAAc;YAACjB,IAAI,EAAC,UAAU;YAACwB,WAAW,EAAC,iBAAiB;YAACvB,KAAK,EAAEV,QAAQ,CAACkB,QAAS;YAACgB,QAAQ,EAAE3B,iBAAkB;YAAC8B,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpD,OAAA;QAAK+C,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBhD,OAAA;UAAK+C,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBhD,OAAA;YAAOqD,IAAI,EAAC,MAAM;YAACN,SAAS,EAAC,cAAc;YAACjB,IAAI,EAAC,SAAS;YAACwB,WAAW,EAAC,eAAe;YAACvB,KAAK,EAAEV,QAAQ,CAACmB,OAAQ;YAACe,QAAQ,EAAE3B,iBAAkB;YAAC8B,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrJ,CAAC,eACNpD,OAAA;UAAK+C,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBhD,OAAA;YAAOqD,IAAI,EAAC,QAAQ;YAACN,SAAS,EAAC,cAAc;YAACjB,IAAI,EAAC,YAAY;YAACwB,WAAW,EAAC,aAAa;YAACvB,KAAK,EAAEV,QAAQ,CAACC,UAAW;YAACiC,QAAQ,EAAE3B,iBAAkB;YAAC8B,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3J,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpD,OAAA;QAAK+C,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBhD,OAAA;UAAK+C,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBhD,OAAA;YAAOqD,IAAI,EAAC,MAAM;YAACN,SAAS,EAAC,cAAc;YAACjB,IAAI,EAAC,aAAa;YAACwB,WAAW,EAAC,cAAc;YAACvB,KAAK,EAAEV,QAAQ,CAACoB,WAAY;YAACc,QAAQ,EAAE3B,iBAAkB;YAAC8B,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5J,CAAC,eACNpD,OAAA;UAAK+C,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBhD,OAAA;YAAOqD,IAAI,EAAC,QAAQ;YAACN,SAAS,EAAC,cAAc;YAACjB,IAAI,EAAC,YAAY;YAACwB,WAAW,EAAC,oBAAoB;YAACvB,KAAK,EAAEV,QAAQ,CAACqB,UAAW;YAACa,QAAQ,EAAE3B,iBAAkB;YAAC8B,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpD,OAAA;QAAQqD,IAAI,EAAC,QAAQ;QAACN,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACrEO,aAAa,iBAAI3D,OAAA;QAAK+C,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAEW;MAAa;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAGPpD,OAAA;MAAK+C,SAAS,EAAC,6DAA6D;MAAAC,QAAA,gBAC1EhD,OAAA;QAAAgD,QAAA,gBACEhD,OAAA;UAAAgD,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC1BQ,kBAAkB,iBACjB5D,OAAA;UAAO+C,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,gCACE,EAACY,kBAAkB;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACR,EACA,CAACQ,kBAAkB,IAAId,IAAI,CAACe,MAAM,GAAG,CAAC,iBACrC7D,OAAA;UAAO+C,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,yBACL,EAACF,IAAI,CAACe,MAAM,EAAC,WACtC;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNpD,OAAA;QACE+C,SAAS,EAAC,iBAAiB;QAC3BS,OAAO,EAAE/B,cAAe;QACxBqC,QAAQ,EAAEC,OAAQ;QAAAf,QAAA,EAEjBe,OAAO,GAAG,YAAY,GAAG;MAAoB;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELW,OAAO,gBACN/D,OAAA,CAACgE,cAAc;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAChBa,KAAK,gBACPjE,OAAA,CAACkE,UAAU;MAAC/C,OAAO,EAAE8C,KAAM;MAACE,OAAO,EAAEzD;IAAU;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAElDpD,OAAA,CAAAE,SAAA;MAAA8C,QAAA,EACGoB,KAAK,CAACC,OAAO,CAACvB,IAAI,CAAC,IAAIA,IAAI,CAACe,MAAM,GAAG,CAAC,GACrCf,IAAI,CAACwB,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,kBAChBxE,OAAA;QAAK+C,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBhD,OAAA;UAAK+C,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBhD,OAAA,CAACyE,YAAY;YAACC,QAAQ,EAAEH,GAAG,CAACG;UAAS;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACNpD,OAAA;UAAK+C,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBhD,OAAA,CAAC2E,cAAc;YAACC,UAAU,EAAEL,GAAG,CAACK;UAAW;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNpD,OAAA;UAAK+C,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBhD,OAAA,CAAC6E,WAAW;YAACC,OAAO,EAAEP,GAAG,CAACO;UAAQ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA,GATuBoB,GAAG;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAU7B,CACN,CAAC,gBAEFpD,OAAA;QAAAgD,QAAA,EAAK;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAC5B,gBACD,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAChD,EAAA,CAhNQD,GAAG;AAAA4E,EAAA,GAAH5E,GAAG;AAkNZ,eAAeA,GAAG;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}