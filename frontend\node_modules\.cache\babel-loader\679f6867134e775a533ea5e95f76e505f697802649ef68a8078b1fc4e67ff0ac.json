{"ast": null, "code": "import axios from 'axios';\nconst BFF_AUTH_URL = 'http://localhost:4000/api/auth';\n\n// Create axios instance for auth requests\nconst authClient = axios.create({\n  baseURL: BFF_AUTH_URL,\n  timeout: 15000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Token storage keys\nconst ACCESS_TOKEN_KEY = 'employee_access_token';\nconst REFRESH_TOKEN_KEY = 'employee_refresh_token';\nconst USER_DATA_KEY = 'employee_user_data';\nclass AuthService {\n  constructor() {\n    this.setupInterceptors();\n  }\n\n  /**\n   * Setup axios interceptors for automatic token handling\n   */\n  setupInterceptors() {\n    // Request interceptor to add token to headers\n    authClient.interceptors.request.use(config => {\n      const token = this.getAccessToken();\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Response interceptor to handle token refresh\n    authClient.interceptors.response.use(response => response, async error => {\n      var _error$response;\n      const originalRequest = error.config;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n          const refreshToken = this.getRefreshToken();\n          if (refreshToken) {\n            const response = await this.refreshAccessToken(refreshToken);\n            const {\n              accessToken\n            } = response.data.tokens;\n            this.setAccessToken(accessToken);\n            originalRequest.headers.Authorization = `Bearer ${accessToken}`;\n            return authClient(originalRequest);\n          }\n        } catch (refreshError) {\n          console.error('Token refresh failed:', refreshError);\n          this.logout();\n          window.location.href = '/login';\n        }\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  /**\n   * Login user with username/email and password\n   * @param {string} identifier - Username or email\n   * @param {string} password - Password\n   * @returns {Promise<Object>} Login response\n   */\n  async login(identifier, password) {\n    try {\n      console.log('🔑 Attempting login for:', identifier);\n      const response = await authClient.post('/login', {\n        identifier,\n        password\n      });\n      const {\n        user,\n        tokens\n      } = response.data.data;\n\n      // Store tokens and user data\n      this.setAccessToken(tokens.accessToken);\n      this.setRefreshToken(tokens.refreshToken);\n      this.setUserData(user);\n      console.log('✅ Login successful for user:', user.username);\n      return response.data;\n    } catch (error) {\n      var _error$response2;\n      console.error('❌ Login failed:', ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message);\n      throw this.handleAuthError(error);\n    }\n  }\n\n  /**\n   * Register new user (admin only)\n   * @param {Object} userData - User registration data\n   * @returns {Promise<Object>} Registration response\n   */\n  async register(userData) {\n    try {\n      console.log('📝 Attempting registration for:', userData.username);\n      const response = await authClient.post('/register', userData);\n      console.log('✅ Registration successful for user:', userData.username);\n      return response.data;\n    } catch (error) {\n      var _error$response3;\n      console.error('❌ Registration failed:', ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || error.message);\n      throw this.handleAuthError(error);\n    }\n  }\n\n  /**\n   * Logout user\n   * @returns {Promise<void>}\n   */\n  async logout() {\n    try {\n      const refreshToken = this.getRefreshToken();\n      if (refreshToken) {\n        await authClient.post('/logout', {\n          refreshToken\n        });\n      }\n      console.log('✅ Logout successful');\n    } catch (error) {\n      console.error('❌ Logout error:', error);\n    } finally {\n      // Clear local storage regardless of API call result\n      this.clearTokens();\n    }\n  }\n\n  /**\n   * Refresh access token\n   * @param {string} refreshToken - Refresh token\n   * @returns {Promise<Object>} Refresh response\n   */\n  async refreshAccessToken(refreshToken) {\n    try {\n      const response = await axios.post(`${BFF_AUTH_URL}/refresh`, {\n        refreshToken\n      });\n      console.log('🔄 Token refreshed successfully');\n      return response.data;\n    } catch (error) {\n      console.error('❌ Token refresh failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get current user profile\n   * @returns {Promise<Object>} User profile\n   */\n  async getCurrentUser() {\n    try {\n      const response = await authClient.get('/me');\n\n      // Update stored user data\n      this.setUserData(response.data.data.user);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Failed to get current user:', error);\n      throw this.handleAuthError(error);\n    }\n  }\n\n  /**\n   * Verify token validity\n   * @returns {Promise<Object>} Verification response\n   */\n  async verifyToken() {\n    try {\n      const response = await authClient.get('/verify');\n      return response.data;\n    } catch (error) {\n      console.error('❌ Token verification failed:', error);\n      throw this.handleAuthError(error);\n    }\n  }\n\n  /**\n   * Get all users (admin only)\n   * @returns {Promise<Object>} Users list\n   */\n  async getAllUsers() {\n    try {\n      const response = await authClient.get('/users');\n      return response.data;\n    } catch (error) {\n      console.error('❌ Failed to get users:', error);\n      throw this.handleAuthError(error);\n    }\n  }\n\n  // Token management methods\n  getAccessToken() {\n    return localStorage.getItem(ACCESS_TOKEN_KEY);\n  }\n  setAccessToken(token) {\n    localStorage.setItem(ACCESS_TOKEN_KEY, token);\n  }\n  getRefreshToken() {\n    return localStorage.getItem(REFRESH_TOKEN_KEY);\n  }\n  setRefreshToken(token) {\n    localStorage.setItem(REFRESH_TOKEN_KEY, token);\n  }\n  getUserData() {\n    const userData = localStorage.getItem(USER_DATA_KEY);\n    return userData ? JSON.parse(userData) : null;\n  }\n  setUserData(userData) {\n    localStorage.setItem(USER_DATA_KEY, JSON.stringify(userData));\n  }\n  clearTokens() {\n    localStorage.removeItem(ACCESS_TOKEN_KEY);\n    localStorage.removeItem(REFRESH_TOKEN_KEY);\n    localStorage.removeItem(USER_DATA_KEY);\n  }\n\n  /**\n   * Check if user is authenticated\n   * @returns {boolean} True if authenticated\n   */\n  isAuthenticated() {\n    const token = this.getAccessToken();\n    const userData = this.getUserData();\n    return !!(token && userData);\n  }\n\n  /**\n   * Check if user has specific role\n   * @param {string} role - Role to check\n   * @returns {boolean} True if user has role\n   */\n  hasRole(role) {\n    const userData = this.getUserData();\n    return (userData === null || userData === void 0 ? void 0 : userData.role) === role || (userData === null || userData === void 0 ? void 0 : userData.role) === 'admin';\n  }\n\n  /**\n   * Check if user is admin\n   * @returns {boolean} True if user is admin\n   */\n  isAdmin() {\n    return this.hasRole('admin');\n  }\n\n  /**\n   * Get current user info from local storage\n   * @returns {Object|null} User data or null\n   */\n  getCurrentUserData() {\n    return this.getUserData();\n  }\n\n  /**\n   * Handle authentication errors\n   * @param {Error} error - Error object\n   * @returns {Error} Formatted error\n   */\n  handleAuthError(error) {\n    var _error$response4, _error$response4$data;\n    if ((_error$response4 = error.response) !== null && _error$response4 !== void 0 && (_error$response4$data = _error$response4.data) !== null && _error$response4$data !== void 0 && _error$response4$data.error) {\n      const {\n        message,\n        code,\n        status\n      } = error.response.data.error;\n      const authError = new Error(message);\n      authError.code = code;\n      authError.status = status;\n      return authError;\n    }\n    return new Error(error.message || 'Authentication failed');\n  }\n\n  /**\n   * Create authenticated axios instance for other services\n   * @returns {Object} Configured axios instance\n   */\n  createAuthenticatedClient(baseURL) {\n    const client = axios.create({\n      baseURL,\n      timeout: 15000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    // Add token to requests\n    client.interceptors.request.use(config => {\n      const token = this.getAccessToken();\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => Promise.reject(error));\n\n    // Handle token refresh on 401\n    client.interceptors.response.use(response => response, async error => {\n      var _error$response5;\n      const originalRequest = error.config;\n      if (((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n          const refreshToken = this.getRefreshToken();\n          if (refreshToken) {\n            const response = await this.refreshAccessToken(refreshToken);\n            const {\n              accessToken\n            } = response.data.tokens;\n            this.setAccessToken(accessToken);\n            originalRequest.headers.Authorization = `Bearer ${accessToken}`;\n            return client(originalRequest);\n          }\n        } catch (refreshError) {\n          this.logout();\n          window.location.href = '/login';\n        }\n      }\n      return Promise.reject(error);\n    });\n    return client;\n  }\n}\nexport default new AuthService();", "map": {"version": 3, "names": ["axios", "BFF_AUTH_URL", "authClient", "create", "baseURL", "timeout", "headers", "ACCESS_TOKEN_KEY", "REFRESH_TOKEN_KEY", "USER_DATA_KEY", "AuthService", "constructor", "setupInterceptors", "interceptors", "request", "use", "config", "token", "getAccessToken", "Authorization", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "getRefreshToken", "refreshAccessToken", "accessToken", "data", "tokens", "setAccessToken", "refreshError", "console", "logout", "window", "location", "href", "login", "identifier", "password", "log", "post", "user", "setRefreshToken", "setUserData", "username", "_error$response2", "message", "handleAuthError", "register", "userData", "_error$response3", "clearTokens", "getCurrentUser", "get", "verifyToken", "getAllUsers", "localStorage", "getItem", "setItem", "getUserData", "JSON", "parse", "stringify", "removeItem", "isAuthenticated", "hasRole", "role", "isAdmin", "getCurrentUserData", "_error$response4", "_error$response4$data", "code", "authError", "Error", "createAuthenticatedClient", "client", "_error$response5"], "sources": ["D:/FRONT-END-BACK-END-COMMUNICATION/frontend/src/services/AuthService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst BFF_AUTH_URL = 'http://localhost:4000/api/auth';\n\n// Create axios instance for auth requests\nconst authClient = axios.create({\n  baseURL: BFF_AUTH_URL,\n  timeout: 15000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Token storage keys\nconst ACCESS_TOKEN_KEY = 'employee_access_token';\nconst REFRESH_TOKEN_KEY = 'employee_refresh_token';\nconst USER_DATA_KEY = 'employee_user_data';\n\nclass AuthService {\n  constructor() {\n    this.setupInterceptors();\n  }\n\n  /**\n   * Setup axios interceptors for automatic token handling\n   */\n  setupInterceptors() {\n    // Request interceptor to add token to headers\n    authClient.interceptors.request.use(\n      (config) => {\n        const token = this.getAccessToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor to handle token refresh\n    authClient.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          try {\n            const refreshToken = this.getRefreshToken();\n            if (refreshToken) {\n              const response = await this.refreshAccessToken(refreshToken);\n              const { accessToken } = response.data.tokens;\n              \n              this.setAccessToken(accessToken);\n              originalRequest.headers.Authorization = `Bearer ${accessToken}`;\n              \n              return authClient(originalRequest);\n            }\n          } catch (refreshError) {\n            console.error('Token refresh failed:', refreshError);\n            this.logout();\n            window.location.href = '/login';\n          }\n        }\n\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  /**\n   * Login user with username/email and password\n   * @param {string} identifier - Username or email\n   * @param {string} password - Password\n   * @returns {Promise<Object>} Login response\n   */\n  async login(identifier, password) {\n    try {\n      console.log('🔑 Attempting login for:', identifier);\n      \n      const response = await authClient.post('/login', {\n        identifier,\n        password\n      });\n\n      const { user, tokens } = response.data.data;\n\n      // Store tokens and user data\n      this.setAccessToken(tokens.accessToken);\n      this.setRefreshToken(tokens.refreshToken);\n      this.setUserData(user);\n\n      console.log('✅ Login successful for user:', user.username);\n      return response.data;\n\n    } catch (error) {\n      console.error('❌ Login failed:', error.response?.data || error.message);\n      throw this.handleAuthError(error);\n    }\n  }\n\n  /**\n   * Register new user (admin only)\n   * @param {Object} userData - User registration data\n   * @returns {Promise<Object>} Registration response\n   */\n  async register(userData) {\n    try {\n      console.log('📝 Attempting registration for:', userData.username);\n      \n      const response = await authClient.post('/register', userData);\n\n      console.log('✅ Registration successful for user:', userData.username);\n      return response.data;\n\n    } catch (error) {\n      console.error('❌ Registration failed:', error.response?.data || error.message);\n      throw this.handleAuthError(error);\n    }\n  }\n\n  /**\n   * Logout user\n   * @returns {Promise<void>}\n   */\n  async logout() {\n    try {\n      const refreshToken = this.getRefreshToken();\n      \n      if (refreshToken) {\n        await authClient.post('/logout', { refreshToken });\n      }\n\n      console.log('✅ Logout successful');\n    } catch (error) {\n      console.error('❌ Logout error:', error);\n    } finally {\n      // Clear local storage regardless of API call result\n      this.clearTokens();\n    }\n  }\n\n  /**\n   * Refresh access token\n   * @param {string} refreshToken - Refresh token\n   * @returns {Promise<Object>} Refresh response\n   */\n  async refreshAccessToken(refreshToken) {\n    try {\n      const response = await axios.post(`${BFF_AUTH_URL}/refresh`, {\n        refreshToken\n      });\n\n      console.log('🔄 Token refreshed successfully');\n      return response.data;\n\n    } catch (error) {\n      console.error('❌ Token refresh failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get current user profile\n   * @returns {Promise<Object>} User profile\n   */\n  async getCurrentUser() {\n    try {\n      const response = await authClient.get('/me');\n      \n      // Update stored user data\n      this.setUserData(response.data.data.user);\n      \n      return response.data;\n    } catch (error) {\n      console.error('❌ Failed to get current user:', error);\n      throw this.handleAuthError(error);\n    }\n  }\n\n  /**\n   * Verify token validity\n   * @returns {Promise<Object>} Verification response\n   */\n  async verifyToken() {\n    try {\n      const response = await authClient.get('/verify');\n      return response.data;\n    } catch (error) {\n      console.error('❌ Token verification failed:', error);\n      throw this.handleAuthError(error);\n    }\n  }\n\n  /**\n   * Get all users (admin only)\n   * @returns {Promise<Object>} Users list\n   */\n  async getAllUsers() {\n    try {\n      const response = await authClient.get('/users');\n      return response.data;\n    } catch (error) {\n      console.error('❌ Failed to get users:', error);\n      throw this.handleAuthError(error);\n    }\n  }\n\n  // Token management methods\n  getAccessToken() {\n    return localStorage.getItem(ACCESS_TOKEN_KEY);\n  }\n\n  setAccessToken(token) {\n    localStorage.setItem(ACCESS_TOKEN_KEY, token);\n  }\n\n  getRefreshToken() {\n    return localStorage.getItem(REFRESH_TOKEN_KEY);\n  }\n\n  setRefreshToken(token) {\n    localStorage.setItem(REFRESH_TOKEN_KEY, token);\n  }\n\n  getUserData() {\n    const userData = localStorage.getItem(USER_DATA_KEY);\n    return userData ? JSON.parse(userData) : null;\n  }\n\n  setUserData(userData) {\n    localStorage.setItem(USER_DATA_KEY, JSON.stringify(userData));\n  }\n\n  clearTokens() {\n    localStorage.removeItem(ACCESS_TOKEN_KEY);\n    localStorage.removeItem(REFRESH_TOKEN_KEY);\n    localStorage.removeItem(USER_DATA_KEY);\n  }\n\n  /**\n   * Check if user is authenticated\n   * @returns {boolean} True if authenticated\n   */\n  isAuthenticated() {\n    const token = this.getAccessToken();\n    const userData = this.getUserData();\n    return !!(token && userData);\n  }\n\n  /**\n   * Check if user has specific role\n   * @param {string} role - Role to check\n   * @returns {boolean} True if user has role\n   */\n  hasRole(role) {\n    const userData = this.getUserData();\n    return userData?.role === role || userData?.role === 'admin';\n  }\n\n  /**\n   * Check if user is admin\n   * @returns {boolean} True if user is admin\n   */\n  isAdmin() {\n    return this.hasRole('admin');\n  }\n\n  /**\n   * Get current user info from local storage\n   * @returns {Object|null} User data or null\n   */\n  getCurrentUserData() {\n    return this.getUserData();\n  }\n\n  /**\n   * Handle authentication errors\n   * @param {Error} error - Error object\n   * @returns {Error} Formatted error\n   */\n  handleAuthError(error) {\n    if (error.response?.data?.error) {\n      const { message, code, status } = error.response.data.error;\n      const authError = new Error(message);\n      authError.code = code;\n      authError.status = status;\n      return authError;\n    }\n    \n    return new Error(error.message || 'Authentication failed');\n  }\n\n  /**\n   * Create authenticated axios instance for other services\n   * @returns {Object} Configured axios instance\n   */\n  createAuthenticatedClient(baseURL) {\n    const client = axios.create({\n      baseURL,\n      timeout: 15000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    // Add token to requests\n    client.interceptors.request.use(\n      (config) => {\n        const token = this.getAccessToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Handle token refresh on 401\n    client.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          try {\n            const refreshToken = this.getRefreshToken();\n            if (refreshToken) {\n              const response = await this.refreshAccessToken(refreshToken);\n              const { accessToken } = response.data.tokens;\n              \n              this.setAccessToken(accessToken);\n              originalRequest.headers.Authorization = `Bearer ${accessToken}`;\n              \n              return client(originalRequest);\n            }\n          } catch (refreshError) {\n            this.logout();\n            window.location.href = '/login';\n          }\n        }\n\n        return Promise.reject(error);\n      }\n    );\n\n    return client;\n  }\n}\n\nexport default new AuthService();\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,gCAAgC;;AAErD;AACA,MAAMC,UAAU,GAAGF,KAAK,CAACG,MAAM,CAAC;EAC9BC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,gBAAgB,GAAG,uBAAuB;AAChD,MAAMC,iBAAiB,GAAG,wBAAwB;AAClD,MAAMC,aAAa,GAAG,oBAAoB;AAE1C,MAAMC,WAAW,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;EACEA,iBAAiBA,CAAA,EAAG;IAClB;IACAV,UAAU,CAACW,YAAY,CAACC,OAAO,CAACC,GAAG,CAChCC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACnC,IAAID,KAAK,EAAE;QACTD,MAAM,CAACV,OAAO,CAACa,aAAa,GAAG,UAAUF,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAI,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACAlB,UAAU,CAACW,YAAY,CAACU,QAAQ,CAACR,GAAG,CACjCQ,QAAQ,IAAKA,QAAQ,EACtB,MAAOH,KAAK,IAAK;MAAA,IAAAI,eAAA;MACf,MAAMC,eAAe,GAAGL,KAAK,CAACJ,MAAM;MAEpC,IAAI,EAAAQ,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;QAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;QAE7B,IAAI;UACF,MAAMC,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;UAC3C,IAAID,YAAY,EAAE;YAChB,MAAML,QAAQ,GAAG,MAAM,IAAI,CAACO,kBAAkB,CAACF,YAAY,CAAC;YAC5D,MAAM;cAAEG;YAAY,CAAC,GAAGR,QAAQ,CAACS,IAAI,CAACC,MAAM;YAE5C,IAAI,CAACC,cAAc,CAACH,WAAW,CAAC;YAChCN,eAAe,CAACnB,OAAO,CAACa,aAAa,GAAG,UAAUY,WAAW,EAAE;YAE/D,OAAO7B,UAAU,CAACuB,eAAe,CAAC;UACpC;QACF,CAAC,CAAC,OAAOU,YAAY,EAAE;UACrBC,OAAO,CAAChB,KAAK,CAAC,uBAAuB,EAAEe,YAAY,CAAC;UACpD,IAAI,CAACE,MAAM,CAAC,CAAC;UACbC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QACjC;MACF;MAEA,OAAOnB,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMqB,KAAKA,CAACC,UAAU,EAAEC,QAAQ,EAAE;IAChC,IAAI;MACFP,OAAO,CAACQ,GAAG,CAAC,0BAA0B,EAAEF,UAAU,CAAC;MAEnD,MAAMnB,QAAQ,GAAG,MAAMrB,UAAU,CAAC2C,IAAI,CAAC,QAAQ,EAAE;QAC/CH,UAAU;QACVC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEG,IAAI;QAAEb;MAAO,CAAC,GAAGV,QAAQ,CAACS,IAAI,CAACA,IAAI;;MAE3C;MACA,IAAI,CAACE,cAAc,CAACD,MAAM,CAACF,WAAW,CAAC;MACvC,IAAI,CAACgB,eAAe,CAACd,MAAM,CAACL,YAAY,CAAC;MACzC,IAAI,CAACoB,WAAW,CAACF,IAAI,CAAC;MAEtBV,OAAO,CAACQ,GAAG,CAAC,8BAA8B,EAAEE,IAAI,CAACG,QAAQ,CAAC;MAC1D,OAAO1B,QAAQ,CAACS,IAAI;IAEtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MAAA,IAAA8B,gBAAA;MACdd,OAAO,CAAChB,KAAK,CAAC,iBAAiB,EAAE,EAAA8B,gBAAA,GAAA9B,KAAK,CAACG,QAAQ,cAAA2B,gBAAA,uBAAdA,gBAAA,CAAgBlB,IAAI,KAAIZ,KAAK,CAAC+B,OAAO,CAAC;MACvE,MAAM,IAAI,CAACC,eAAe,CAAChC,KAAK,CAAC;IACnC;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMiC,QAAQA,CAACC,QAAQ,EAAE;IACvB,IAAI;MACFlB,OAAO,CAACQ,GAAG,CAAC,iCAAiC,EAAEU,QAAQ,CAACL,QAAQ,CAAC;MAEjE,MAAM1B,QAAQ,GAAG,MAAMrB,UAAU,CAAC2C,IAAI,CAAC,WAAW,EAAES,QAAQ,CAAC;MAE7DlB,OAAO,CAACQ,GAAG,CAAC,qCAAqC,EAAEU,QAAQ,CAACL,QAAQ,CAAC;MACrE,OAAO1B,QAAQ,CAACS,IAAI;IAEtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MAAA,IAAAmC,gBAAA;MACdnB,OAAO,CAAChB,KAAK,CAAC,wBAAwB,EAAE,EAAAmC,gBAAA,GAAAnC,KAAK,CAACG,QAAQ,cAAAgC,gBAAA,uBAAdA,gBAAA,CAAgBvB,IAAI,KAAIZ,KAAK,CAAC+B,OAAO,CAAC;MAC9E,MAAM,IAAI,CAACC,eAAe,CAAChC,KAAK,CAAC;IACnC;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMiB,MAAMA,CAAA,EAAG;IACb,IAAI;MACF,MAAMT,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;MAE3C,IAAID,YAAY,EAAE;QAChB,MAAM1B,UAAU,CAAC2C,IAAI,CAAC,SAAS,EAAE;UAAEjB;QAAa,CAAC,CAAC;MACpD;MAEAQ,OAAO,CAACQ,GAAG,CAAC,qBAAqB,CAAC;IACpC,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACzC,CAAC,SAAS;MACR;MACA,IAAI,CAACoC,WAAW,CAAC,CAAC;IACpB;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAM1B,kBAAkBA,CAACF,YAAY,EAAE;IACrC,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMvB,KAAK,CAAC6C,IAAI,CAAC,GAAG5C,YAAY,UAAU,EAAE;QAC3D2B;MACF,CAAC,CAAC;MAEFQ,OAAO,CAACQ,GAAG,CAAC,iCAAiC,CAAC;MAC9C,OAAOrB,QAAQ,CAACS,IAAI;IAEtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMqC,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAMlC,QAAQ,GAAG,MAAMrB,UAAU,CAACwD,GAAG,CAAC,KAAK,CAAC;;MAE5C;MACA,IAAI,CAACV,WAAW,CAACzB,QAAQ,CAACS,IAAI,CAACA,IAAI,CAACc,IAAI,CAAC;MAEzC,OAAOvB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAM,IAAI,CAACgC,eAAe,CAAChC,KAAK,CAAC;IACnC;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMuC,WAAWA,CAAA,EAAG;IAClB,IAAI;MACF,MAAMpC,QAAQ,GAAG,MAAMrB,UAAU,CAACwD,GAAG,CAAC,SAAS,CAAC;MAChD,OAAOnC,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAM,IAAI,CAACgC,eAAe,CAAChC,KAAK,CAAC;IACnC;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMwC,WAAWA,CAAA,EAAG;IAClB,IAAI;MACF,MAAMrC,QAAQ,GAAG,MAAMrB,UAAU,CAACwD,GAAG,CAAC,QAAQ,CAAC;MAC/C,OAAOnC,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAM,IAAI,CAACgC,eAAe,CAAChC,KAAK,CAAC;IACnC;EACF;;EAEA;EACAF,cAAcA,CAAA,EAAG;IACf,OAAO2C,YAAY,CAACC,OAAO,CAACvD,gBAAgB,CAAC;EAC/C;EAEA2B,cAAcA,CAACjB,KAAK,EAAE;IACpB4C,YAAY,CAACE,OAAO,CAACxD,gBAAgB,EAAEU,KAAK,CAAC;EAC/C;EAEAY,eAAeA,CAAA,EAAG;IAChB,OAAOgC,YAAY,CAACC,OAAO,CAACtD,iBAAiB,CAAC;EAChD;EAEAuC,eAAeA,CAAC9B,KAAK,EAAE;IACrB4C,YAAY,CAACE,OAAO,CAACvD,iBAAiB,EAAES,KAAK,CAAC;EAChD;EAEA+C,WAAWA,CAAA,EAAG;IACZ,MAAMV,QAAQ,GAAGO,YAAY,CAACC,OAAO,CAACrD,aAAa,CAAC;IACpD,OAAO6C,QAAQ,GAAGW,IAAI,CAACC,KAAK,CAACZ,QAAQ,CAAC,GAAG,IAAI;EAC/C;EAEAN,WAAWA,CAACM,QAAQ,EAAE;IACpBO,YAAY,CAACE,OAAO,CAACtD,aAAa,EAAEwD,IAAI,CAACE,SAAS,CAACb,QAAQ,CAAC,CAAC;EAC/D;EAEAE,WAAWA,CAAA,EAAG;IACZK,YAAY,CAACO,UAAU,CAAC7D,gBAAgB,CAAC;IACzCsD,YAAY,CAACO,UAAU,CAAC5D,iBAAiB,CAAC;IAC1CqD,YAAY,CAACO,UAAU,CAAC3D,aAAa,CAAC;EACxC;;EAEA;AACF;AACA;AACA;EACE4D,eAAeA,CAAA,EAAG;IAChB,MAAMpD,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,MAAMoC,QAAQ,GAAG,IAAI,CAACU,WAAW,CAAC,CAAC;IACnC,OAAO,CAAC,EAAE/C,KAAK,IAAIqC,QAAQ,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;EACEgB,OAAOA,CAACC,IAAI,EAAE;IACZ,MAAMjB,QAAQ,GAAG,IAAI,CAACU,WAAW,CAAC,CAAC;IACnC,OAAO,CAAAV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiB,IAAI,MAAKA,IAAI,IAAI,CAAAjB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiB,IAAI,MAAK,OAAO;EAC9D;;EAEA;AACF;AACA;AACA;EACEC,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACF,OAAO,CAAC,OAAO,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;EACEG,kBAAkBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACT,WAAW,CAAC,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;EACEZ,eAAeA,CAAChC,KAAK,EAAE;IAAA,IAAAsD,gBAAA,EAAAC,qBAAA;IACrB,KAAAD,gBAAA,GAAItD,KAAK,CAACG,QAAQ,cAAAmD,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1C,IAAI,cAAA2C,qBAAA,eAApBA,qBAAA,CAAsBvD,KAAK,EAAE;MAC/B,MAAM;QAAE+B,OAAO;QAAEyB,IAAI;QAAElD;MAAO,CAAC,GAAGN,KAAK,CAACG,QAAQ,CAACS,IAAI,CAACZ,KAAK;MAC3D,MAAMyD,SAAS,GAAG,IAAIC,KAAK,CAAC3B,OAAO,CAAC;MACpC0B,SAAS,CAACD,IAAI,GAAGA,IAAI;MACrBC,SAAS,CAACnD,MAAM,GAAGA,MAAM;MACzB,OAAOmD,SAAS;IAClB;IAEA,OAAO,IAAIC,KAAK,CAAC1D,KAAK,CAAC+B,OAAO,IAAI,uBAAuB,CAAC;EAC5D;;EAEA;AACF;AACA;AACA;EACE4B,yBAAyBA,CAAC3E,OAAO,EAAE;IACjC,MAAM4E,MAAM,GAAGhF,KAAK,CAACG,MAAM,CAAC;MAC1BC,OAAO;MACPC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;;IAEF;IACA0E,MAAM,CAACnE,YAAY,CAACC,OAAO,CAACC,GAAG,CAC5BC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACnC,IAAID,KAAK,EAAE;QACTD,MAAM,CAACV,OAAO,CAACa,aAAa,GAAG,UAAUF,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAI,KAAK,IAAKC,OAAO,CAACC,MAAM,CAACF,KAAK,CACjC,CAAC;;IAED;IACA4D,MAAM,CAACnE,YAAY,CAACU,QAAQ,CAACR,GAAG,CAC7BQ,QAAQ,IAAKA,QAAQ,EACtB,MAAOH,KAAK,IAAK;MAAA,IAAA6D,gBAAA;MACf,MAAMxD,eAAe,GAAGL,KAAK,CAACJ,MAAM;MAEpC,IAAI,EAAAiE,gBAAA,GAAA7D,KAAK,CAACG,QAAQ,cAAA0D,gBAAA,uBAAdA,gBAAA,CAAgBvD,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;QAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;QAE7B,IAAI;UACF,MAAMC,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;UAC3C,IAAID,YAAY,EAAE;YAChB,MAAML,QAAQ,GAAG,MAAM,IAAI,CAACO,kBAAkB,CAACF,YAAY,CAAC;YAC5D,MAAM;cAAEG;YAAY,CAAC,GAAGR,QAAQ,CAACS,IAAI,CAACC,MAAM;YAE5C,IAAI,CAACC,cAAc,CAACH,WAAW,CAAC;YAChCN,eAAe,CAACnB,OAAO,CAACa,aAAa,GAAG,UAAUY,WAAW,EAAE;YAE/D,OAAOiD,MAAM,CAACvD,eAAe,CAAC;UAChC;QACF,CAAC,CAAC,OAAOU,YAAY,EAAE;UACrB,IAAI,CAACE,MAAM,CAAC,CAAC;UACbC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QACjC;MACF;MAEA,OAAOnB,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;IAED,OAAO4D,MAAM;EACf;AACF;AAEA,eAAe,IAAItE,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}