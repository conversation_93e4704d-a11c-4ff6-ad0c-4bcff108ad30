{"ast": null, "code": "var _jsxFileName = \"D:\\\\FRONT-END-BACK-END-COMMUNICATION\\\\frontend\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport AuthService from '../services/AuthService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Login = ({\n  onLogin\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    identifier: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (AuthService.isAuthenticated()) {\n      var _location$state, _location$state$from;\n      const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/';\n      navigate(from, {\n        replace: true\n      });\n    }\n  }, [navigate, location]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      var _location$state2, _location$state2$from;\n      const response = await AuthService.login(formData.identifier, formData.password);\n\n      // Call parent callback if provided\n      if (onLogin) {\n        onLogin(response.data.user);\n      }\n\n      // Redirect to intended page or dashboard\n      const from = ((_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : (_location$state2$from = _location$state2.from) === null || _location$state2$from === void 0 ? void 0 : _location$state2$from.pathname) || '/';\n      navigate(from, {\n        replace: true\n      });\n    } catch (error) {\n      console.error('Login error:', error);\n      setError(error.message || 'Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const isFormValid = formData.identifier.trim() && formData.password.trim();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-vh-100 d-flex align-items-center justify-content-center bg-light\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6 col-lg-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card shadow\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-body p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"card-title mb-2\",\n                  children: \"\\uD83D\\uDD10 Employee Portal\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted\",\n                  children: \"Sign in to your account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 72,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"alert alert-danger alert-dismissible fade show\",\n                role: \"alert\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-exclamation-triangle-fill me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 21\n                }, this), error, /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-close\",\n                  onClick: () => setError(''),\n                  \"aria-label\": \"Close\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSubmit,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"identifier\",\n                    className: \"form-label\",\n                    children: \"Username or Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 92,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"input-group\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"input-group-text\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"bi bi-person\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 97,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 96,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      className: \"form-control\",\n                      id: \"identifier\",\n                      name: \"identifier\",\n                      value: formData.identifier,\n                      onChange: handleInputChange,\n                      placeholder: \"Enter username or email\",\n                      required: true,\n                      autoComplete: \"username\",\n                      disabled: loading\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 99,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"password\",\n                    className: \"form-label\",\n                    children: \"Password\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"input-group\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"input-group-text\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"bi bi-lock\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 120,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 119,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: showPassword ? 'text' : 'password',\n                      className: \"form-control\",\n                      id: \"password\",\n                      name: \"password\",\n                      value: formData.password,\n                      onChange: handleInputChange,\n                      placeholder: \"Enter password\",\n                      required: true,\n                      autoComplete: \"current-password\",\n                      disabled: loading\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 122,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"btn btn-outline-secondary\",\n                      onClick: () => setShowPassword(!showPassword),\n                      disabled: loading,\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: `bi ${showPassword ? 'bi-eye-slash' : 'bi-eye'}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 140,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-grid\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"submit\",\n                    className: \"btn btn-primary\",\n                    disabled: !isFormValid || loading,\n                    children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"spinner-border spinner-border-sm me-2\",\n                        role: \"status\",\n                        \"aria-hidden\": \"true\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 153,\n                        columnNumber: 27\n                      }, this), \"Signing in...\"]\n                    }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"bi bi-box-arrow-in-right me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 158,\n                        columnNumber: 27\n                      }, this), \"Sign In\"]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 p-3 bg-light rounded\",\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"text-muted mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bi bi-info-circle me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 21\n                  }, this), \"Demo Credentials\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Admin:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 175,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 175,\n                        columnNumber: 48\n                      }, this), \"Username: admin\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 176,\n                        columnNumber: 40\n                      }, this), \"Password: admin123\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"User:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 182,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 182,\n                        columnNumber: 47\n                      }, this), \"Username: user\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 183,\n                        columnNumber: 39\n                      }, this), \"Password: user123\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mt-4\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"Employee Management System v2.0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"z7OHX79lL4VCEGIHR8yxAb0f/2A=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "AuthService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "onLogin", "_s", "formData", "setFormData", "identifier", "password", "loading", "setLoading", "error", "setError", "showPassword", "setShowPassword", "navigate", "location", "isAuthenticated", "_location$state", "_location$state$from", "from", "state", "pathname", "replace", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "_location$state2", "_location$state2$from", "response", "login", "data", "user", "console", "message", "isFormValid", "trim", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "type", "onClick", "onSubmit", "htmlFor", "id", "onChange", "placeholder", "required", "autoComplete", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/FRONT-END-BACK-END-COMMUNICATION/frontend/src/components/Login.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport AuthService from '../services/AuthService';\n\nconst Login = ({ onLogin }) => {\n  const [formData, setFormData] = useState({\n    identifier: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (AuthService.isAuthenticated()) {\n      const from = location.state?.from?.pathname || '/';\n      navigate(from, { replace: true });\n    }\n  }, [navigate, location]);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await AuthService.login(formData.identifier, formData.password);\n      \n      // Call parent callback if provided\n      if (onLogin) {\n        onLogin(response.data.user);\n      }\n\n      // Redirect to intended page or dashboard\n      const from = location.state?.from?.pathname || '/';\n      navigate(from, { replace: true });\n\n    } catch (error) {\n      console.error('Login error:', error);\n      setError(error.message || 'Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const isFormValid = formData.identifier.trim() && formData.password.trim();\n\n  return (\n    <div className=\"min-vh-100 d-flex align-items-center justify-content-center bg-light\">\n      <div className=\"container\">\n        <div className=\"row justify-content-center\">\n          <div className=\"col-md-6 col-lg-4\">\n            <div className=\"card shadow\">\n              <div className=\"card-body p-4\">\n                {/* Header */}\n                <div className=\"text-center mb-4\">\n                  <h2 className=\"card-title mb-2\">🔐 Employee Portal</h2>\n                  <p className=\"text-muted\">Sign in to your account</p>\n                </div>\n\n                {/* Error Alert */}\n                {error && (\n                  <div className=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n                    <i className=\"bi bi-exclamation-triangle-fill me-2\"></i>\n                    {error}\n                    <button \n                      type=\"button\" \n                      className=\"btn-close\" \n                      onClick={() => setError('')}\n                      aria-label=\"Close\"\n                    ></button>\n                  </div>\n                )}\n\n                {/* Login Form */}\n                <form onSubmit={handleSubmit}>\n                  <div className=\"mb-3\">\n                    <label htmlFor=\"identifier\" className=\"form-label\">\n                      Username or Email\n                    </label>\n                    <div className=\"input-group\">\n                      <span className=\"input-group-text\">\n                        <i className=\"bi bi-person\"></i>\n                      </span>\n                      <input\n                        type=\"text\"\n                        className=\"form-control\"\n                        id=\"identifier\"\n                        name=\"identifier\"\n                        value={formData.identifier}\n                        onChange={handleInputChange}\n                        placeholder=\"Enter username or email\"\n                        required\n                        autoComplete=\"username\"\n                        disabled={loading}\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"mb-3\">\n                    <label htmlFor=\"password\" className=\"form-label\">\n                      Password\n                    </label>\n                    <div className=\"input-group\">\n                      <span className=\"input-group-text\">\n                        <i className=\"bi bi-lock\"></i>\n                      </span>\n                      <input\n                        type={showPassword ? 'text' : 'password'}\n                        className=\"form-control\"\n                        id=\"password\"\n                        name=\"password\"\n                        value={formData.password}\n                        onChange={handleInputChange}\n                        placeholder=\"Enter password\"\n                        required\n                        autoComplete=\"current-password\"\n                        disabled={loading}\n                      />\n                      <button\n                        type=\"button\"\n                        className=\"btn btn-outline-secondary\"\n                        onClick={() => setShowPassword(!showPassword)}\n                        disabled={loading}\n                      >\n                        <i className={`bi ${showPassword ? 'bi-eye-slash' : 'bi-eye'}`}></i>\n                      </button>\n                    </div>\n                  </div>\n\n                  <div className=\"d-grid\">\n                    <button\n                      type=\"submit\"\n                      className=\"btn btn-primary\"\n                      disabled={!isFormValid || loading}\n                    >\n                      {loading ? (\n                        <>\n                          <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                          Signing in...\n                        </>\n                      ) : (\n                        <>\n                          <i className=\"bi bi-box-arrow-in-right me-2\"></i>\n                          Sign In\n                        </>\n                      )}\n                    </button>\n                  </div>\n                </form>\n\n                {/* Demo Credentials */}\n                <div className=\"mt-4 p-3 bg-light rounded\">\n                  <h6 className=\"text-muted mb-2\">\n                    <i className=\"bi bi-info-circle me-1\"></i>\n                    Demo Credentials\n                  </h6>\n                  <div className=\"row\">\n                    <div className=\"col-6\">\n                      <small className=\"text-muted\">\n                        <strong>Admin:</strong><br />\n                        Username: admin<br />\n                        Password: admin123\n                      </small>\n                    </div>\n                    <div className=\"col-6\">\n                      <small className=\"text-muted\">\n                        <strong>User:</strong><br />\n                        Username: user<br />\n                        Password: user123\n                      </small>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Footer */}\n                <div className=\"text-center mt-4\">\n                  <small className=\"text-muted\">\n                    Employee Management System v2.0\n                  </small>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC;IACvCc,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMsB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd,IAAIG,WAAW,CAACoB,eAAe,CAAC,CAAC,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACjC,MAAMC,IAAI,GAAG,EAAAF,eAAA,GAAAF,QAAQ,CAACK,KAAK,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBE,IAAI,cAAAD,oBAAA,uBAApBA,oBAAA,CAAsBG,QAAQ,KAAI,GAAG;MAClDP,QAAQ,CAACK,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACR,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAExB,MAAMQ,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCtB,WAAW,CAACuB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIhB,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBrB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACF,MAAMC,QAAQ,GAAG,MAAMrC,WAAW,CAACsC,KAAK,CAAC9B,QAAQ,CAACE,UAAU,EAAEF,QAAQ,CAACG,QAAQ,CAAC;;MAEhF;MACA,IAAIL,OAAO,EAAE;QACXA,OAAO,CAAC+B,QAAQ,CAACE,IAAI,CAACC,IAAI,CAAC;MAC7B;;MAEA;MACA,MAAMjB,IAAI,GAAG,EAAAY,gBAAA,GAAAhB,QAAQ,CAACK,KAAK,cAAAW,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBZ,IAAI,cAAAa,qBAAA,uBAApBA,qBAAA,CAAsBX,QAAQ,KAAI,GAAG;MAClDP,QAAQ,CAACK,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IAEnC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd2B,OAAO,CAAC3B,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCC,QAAQ,CAACD,KAAK,CAAC4B,OAAO,IAAI,iCAAiC,CAAC;IAC9D,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,WAAW,GAAGnC,QAAQ,CAACE,UAAU,CAACkC,IAAI,CAAC,CAAC,IAAIpC,QAAQ,CAACG,QAAQ,CAACiC,IAAI,CAAC,CAAC;EAE1E,oBACE1C,OAAA;IAAK2C,SAAS,EAAC,sEAAsE;IAAAC,QAAA,eACnF5C,OAAA;MAAK2C,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxB5C,OAAA;QAAK2C,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzC5C,OAAA;UAAK2C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC5C,OAAA;YAAK2C,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1B5C,OAAA;cAAK2C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAE5B5C,OAAA;gBAAK2C,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B5C,OAAA;kBAAI2C,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvDhD,OAAA;kBAAG2C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,EAGLpC,KAAK,iBACJZ,OAAA;gBAAK2C,SAAS,EAAC,gDAAgD;gBAACM,IAAI,EAAC,OAAO;gBAAAL,QAAA,gBAC1E5C,OAAA;kBAAG2C,SAAS,EAAC;gBAAsC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACvDpC,KAAK,eACNZ,OAAA;kBACEkD,IAAI,EAAC,QAAQ;kBACbP,SAAS,EAAC,WAAW;kBACrBQ,OAAO,EAAEA,CAAA,KAAMtC,QAAQ,CAAC,EAAE,CAAE;kBAC5B,cAAW;gBAAO;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CACN,eAGDhD,OAAA;gBAAMoD,QAAQ,EAAErB,YAAa;gBAAAa,QAAA,gBAC3B5C,OAAA;kBAAK2C,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB5C,OAAA;oBAAOqD,OAAO,EAAC,YAAY;oBAACV,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAEnD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRhD,OAAA;oBAAK2C,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B5C,OAAA;sBAAM2C,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAChC5C,OAAA;wBAAG2C,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,eACPhD,OAAA;sBACEkD,IAAI,EAAC,MAAM;sBACXP,SAAS,EAAC,cAAc;sBACxBW,EAAE,EAAC,YAAY;sBACf3B,IAAI,EAAC,YAAY;sBACjBC,KAAK,EAAEtB,QAAQ,CAACE,UAAW;sBAC3B+C,QAAQ,EAAE9B,iBAAkB;sBAC5B+B,WAAW,EAAC,yBAAyB;sBACrCC,QAAQ;sBACRC,YAAY,EAAC,UAAU;sBACvBC,QAAQ,EAAEjD;oBAAQ;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENhD,OAAA;kBAAK2C,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB5C,OAAA;oBAAOqD,OAAO,EAAC,UAAU;oBAACV,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAEjD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRhD,OAAA;oBAAK2C,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B5C,OAAA;sBAAM2C,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAChC5C,OAAA;wBAAG2C,SAAS,EAAC;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC,eACPhD,OAAA;sBACEkD,IAAI,EAAEpC,YAAY,GAAG,MAAM,GAAG,UAAW;sBACzC6B,SAAS,EAAC,cAAc;sBACxBW,EAAE,EAAC,UAAU;sBACb3B,IAAI,EAAC,UAAU;sBACfC,KAAK,EAAEtB,QAAQ,CAACG,QAAS;sBACzB8C,QAAQ,EAAE9B,iBAAkB;sBAC5B+B,WAAW,EAAC,gBAAgB;sBAC5BC,QAAQ;sBACRC,YAAY,EAAC,kBAAkB;sBAC/BC,QAAQ,EAAEjD;oBAAQ;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACFhD,OAAA;sBACEkD,IAAI,EAAC,QAAQ;sBACbP,SAAS,EAAC,2BAA2B;sBACrCQ,OAAO,EAAEA,CAAA,KAAMpC,eAAe,CAAC,CAACD,YAAY,CAAE;sBAC9C6C,QAAQ,EAAEjD,OAAQ;sBAAAkC,QAAA,eAElB5C,OAAA;wBAAG2C,SAAS,EAAE,MAAM7B,YAAY,GAAG,cAAc,GAAG,QAAQ;sBAAG;wBAAA+B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENhD,OAAA;kBAAK2C,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACrB5C,OAAA;oBACEkD,IAAI,EAAC,QAAQ;oBACbP,SAAS,EAAC,iBAAiB;oBAC3BgB,QAAQ,EAAE,CAAClB,WAAW,IAAI/B,OAAQ;oBAAAkC,QAAA,EAEjClC,OAAO,gBACNV,OAAA,CAAAE,SAAA;sBAAA0C,QAAA,gBACE5C,OAAA;wBAAM2C,SAAS,EAAC,uCAAuC;wBAACM,IAAI,EAAC,QAAQ;wBAAC,eAAY;sBAAM;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,iBAElG;oBAAA,eAAE,CAAC,gBAEHhD,OAAA,CAAAE,SAAA;sBAAA0C,QAAA,gBACE5C,OAAA;wBAAG2C,SAAS,EAAC;sBAA+B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,WAEnD;oBAAA,eAAE;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGPhD,OAAA;gBAAK2C,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC5C,OAAA;kBAAI2C,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC7B5C,OAAA;oBAAG2C,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,oBAE5C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhD,OAAA;kBAAK2C,SAAS,EAAC,KAAK;kBAAAC,QAAA,gBAClB5C,OAAA;oBAAK2C,SAAS,EAAC,OAAO;oBAAAC,QAAA,eACpB5C,OAAA;sBAAO2C,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBAC3B5C,OAAA;wBAAA4C,QAAA,EAAQ;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAAAhD,OAAA;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,mBACd,eAAAhD,OAAA;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,sBAEvB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNhD,OAAA;oBAAK2C,SAAS,EAAC,OAAO;oBAAAC,QAAA,eACpB5C,OAAA;sBAAO2C,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBAC3B5C,OAAA;wBAAA4C,QAAA,EAAQ;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAAAhD,OAAA;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,kBACd,eAAAhD,OAAA;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,qBAEtB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNhD,OAAA;gBAAK2C,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/B5C,OAAA;kBAAO2C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAE9B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAtMIF,KAAK;EAAA,QASQP,WAAW,EACXC,WAAW;AAAA;AAAA+D,EAAA,GAVxBzD,KAAK;AAwMX,eAAeA,KAAK;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}